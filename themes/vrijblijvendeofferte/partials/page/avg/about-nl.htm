{% if tos_privacy_popup and tos_privacy.about_nl.about_title %}
    {% set urlRoot = this.request.root() %}
    {% set urlClean = urlRoot | replace({'https://': '', 'http://': '', 'www.': ''}) | title %}

    <div @keyup.window.escape="about = false">
        <span class="block text-gray-600 hover:text-gray-700 hover:underline cursor-pointer whitespace-nowrap" @click="about = true" x-ref="about_modal_link">{{ tos_privacy.about_nl.about_link }}</span>

        {% ajaxPartial 'ui/modal' body id="about" %}

            <div class="w-full mb-4 pb-4 border-b flex justify-between items-center">
                <h2 class="mb-0 md:mb-0 lg:mb-0 xl:mb-0">{{ tos_privacy.about_nl.about_title | replace({ '{website}': urlClean }) }}</h2>
                <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" @click="about = false">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6 prose prose-primary lg:prose-lg text-gray-800 max-w-none">
                {{ tos_privacy.about_nl.about | replace({
                    '{website}': urlClean,
                    'href="/disclaimer"': 'href="#disclaimer" @click="about = false;$nextTick(() => { disclaimer = true; });"',
                    'href="/privacy-verklaring"': 'href="#privacy-verklaring" @click="about = false;$nextTick(() => { privacy_statement = true; });"',
                    'href="/cookie-statement"': 'href="#cookie-statement" @click="about = false;$nextTick(() => { cookies = true; });"',
                }) | content }}
            </div>

            <div class="flex flex-wrap md:grid md:grid-cols-2 items-center bg-white overflow-hidden mt-16">
                <div class="order-2 md:order-1 h-full">
                    <img src="https://www.besteleads.nl/storage/app/resources/resize/786_0_0_0_auto/teamfoto_2becdc3355e05d68cf989b852b7a05d5.webp" alt="" class="w-full h-full object-cover lazy">
                </div>
                <div class="order-1 md:order-2">
                    <div class="px-8">
                        <div class="mb-8">
                            <h3 id="partner_form_title" class="text-xl lg:text-2xl text-gray-800 font-bold">{{ 'Wil je feedback geven, klant worden of heb je een andere opmerking of suggestie?'|_ }}</h3>
                        </div>

                        {% component 'PartnerForm2' %}
                    </div>
                </div>
            </div>

            <div class="w-full border-t mt-8 pt-4">
                <button type="button" @click="about = false" class="py-3 px-6 border shadow-sm bg-white text-gray-600 font-medium rounded-md hover:bg-gray-50 hover:text-gray-800">{{ 'Venster sluiten'|_ }}</button>
            </div>
        {% endpartial %}
    </div>
{% endif %}

{% if tos_privacy.about_nl.about_title %}
    {% for item in footerMenu.menuItems %}
        {% if item.title != "Over ons" and item.title != "Qui sommes-nous ?" and item.title != "Qui sommes-nous ?" %}
            <a href="{{ item.url }}" {% if item.viewBag.isExternal %}target="_blank"{% endif %} class="block text-gray-600 hover:text-gray-700 hover:underline">{{ item.title }}</a>
        {% endif %}
    {% endfor %}
{% else %}
    {% for item in footerMenu.menuItems %}
        <a href="{{ item.url }}" {% if item.viewBag.isExternal %}target="_blank"{% endif %} class="block text-gray-600 hover:text-gray-700 hover:underline">{{ item.title }}</a>
    {% endfor %}
{% endif %}
