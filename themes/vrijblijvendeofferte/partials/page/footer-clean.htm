[staticMenu footerMenu]
code = "footer"

[staticMenu footerMenu2]
code = "footer-onder"

[SiteConfig]

[renderForm PartnerForm2]
formCode = "contact-form-2"
==
<footer
    class="px-2 py-6 lg:py-10 bg-secondary-500"
    x-data="{
        terms_of_service: {{ input('terms') ? 'true' : 'false' }},
        privacy_statement: {{ input('privacy') ? 'true' : 'false' }},
        cookies: {{ input('cookies') ? 'true' : 'false' }},
        disclaimer: {{ input('disclaimer') ? 'true' : 'false' }},
        about: {{ input('about') ? 'true' : 'false' }}
    }">
    <div class="container">

        <div class="bg-white p-4 lg:p-8 rounded-lg shadow-xl">

            <div class="grid grid-cols-3 gap-6 lg:grid-cols-4 lg:gap-12">

                <div class=" col-span-3 lg:col-span-1">
                    <a href="#" title="to homepage"><img src="/storage/app/media{{ this.theme.company_logo }}" alt="{{ this.theme.company_name }}" class="h-14"></a>
                </div>

                <div class="text-gray-600 lg:space-y-2 col-span-3 lg:col-span-1">
                    <span class="block">{{ this.theme.company_street }} {{ this.theme.company_housenumber }}</span>
                    <span class="block">{{ this.theme.company_zipcode }} {{ 'te'|_ }} {{ this.theme.company_city }}</span>
                </div>

                <div class="text-gray-600 lg:space-y-2  col-span-3 lg:col-span-1">
                    {% if this.theme.company_kvk %}<span class="block">{{ this.theme.kvk_label|default('KvK') }}: {{ this.theme.company_kvk }}</span>{% endif %}
                    {% if this.theme.company_btw %}<span class="block">{{ this.theme.btw_label|default('BTW') }}: {{ this.theme.company_btw }}</span>{% endif %}
                </div>

                <div>
                    <div class="w-full">
                        <nav class="lg:space-y-2">
                            {% if tos_privacy_language == 'nl' %}
                                {% partial 'page/avg/about-nl' %}
                            {% elseif tos_privacy_language == 'nl-be' %}
                                {% partial 'page/avg/about-be' %}
                            {% elseif tos_privacy_language == 'de' %}
                                {% partial 'page/avg/about-de' %}
                            {% elseif tos_privacy_language == 'en' %}
                                {% partial 'page/avg/about-en' %}
                            {% elseif tos_privacy_language == 'fr' %}
                                {% partial 'page/avg/about-fr' %}
                            {% elseif tos_privacy_language == 'be-fr' %}
                                {% partial 'page/avg/about-be-fr' %}
                            {% elseif tos_privacy_language == 'esp' %}
                                {% partial 'page/avg/about-esp' %}
                            {% elseif tos_privacy_language == 'us' %}
                                {% partial 'page/avg/about-us' %}
                            {% else %}
                                {% partial 'page/avg/about-default' %}
                            {% endif %}
                        </nav>
                    </div>
                </div>

                <div class="space-y-4 col-span-2 lg:hidden">
                    {% if tos_privacy_popup %}
                        {% partial 'page/avg/avg-plugin' footer="clean" %}
                    {% elseif this.theme.tos_privacy_popup %}
                        {% partial 'page/avg/avg-theme' %}
                    {% endif %}
                </div>

            </div>

            <div class="hidden mt-8 lg:flex flex-wrap items-center justify-center space-y-4 lg:space-y-0 lg:space-x-8">
                {% if tos_privacy_popup %}
                    {% partial 'page/avg/avg-plugin' footer="clean" %}
                {% elseif this.theme.tos_privacy_popup %}
                    {% partial 'page/avg/avg-theme' %}
                {% endif %}
            </div>

        </div>

    </div>

</footer>
