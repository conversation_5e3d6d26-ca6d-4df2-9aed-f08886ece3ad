{% set type = "button" %}
{% if item.button_type == "button" or offerte_button %}
    {% set type = "button" %}
{% endif %}
{% if url == "#" %}
    {% set type = "button" %}
{% endif %}
{% if item.button_type == "link" or link_button %}
    {% set type = "link" %}
{% endif %}
{% if url|length > 0 and url != "#" %}
    {% set type = "link" %}
{% endif %}

{% if type == "link" %}

  <a
  href="{{ url }}"
  class="
    text-center
    {{ size == 'lg' ? 'py-4 px-6 text-xl md:text-lgr font-bold' }}
    {{ size == 'base' ? 'py-3 px-5 text-xl md:text-xl font-bold' }}
    {{ size == 'sm' ? 'py-2 px-4 text-base md:text-lg font-semibold' }}
    text-white
    leading-none
    tracking-tight
    rounded-md
    {{ style == 'primary' ? 'bg-primary-500 hover:bg-primary-600' }}
    {{ style == 'secondary' ? 'bg-orange-500 hover:bg-orange-600' }}
    {{ style == 'tertiary' ? 'bg-tertiary-500 hover:bg-tertiary-600' }}
    {{ style == 'darkblue' ? 'bg-secondary-500 hover:bg-secondary-600' }}
    shadow-lg
    hover:shadow-md
    {{ fullWidth == 'yes' ? 'block w-full' : 'inline-block md:inline-flex items-center md:w-auto md:text-left' }}"
  {{ external ? 'target="_blank"' }}
  >
  {% if icon %}<i class="fa-solid fa-{{ icon }} mr-4 leading-none text-white {{ style == 'tertiary' ? 'text-primary-700' }}"></i>{% endif %}
  {{ text }}
</a>

{% else %}

<button
    type="button"
    class="
      text-center
      {{ size == 'lg' ? 'py-4 px-6 text-xl md:text-lgr font-bold' }}
      {{ size == 'base' ? 'py-3 px-5 text-xl md:text-xl font-bold' }}
      {{ size == 'sm' ? 'py-2 px-4 text-base md:text-lg font-semibold' }}
      text-white
      leading-none
      tracking-tight
      rounded-md
      {{ style == 'primary' ? 'bg-primary-500 hover:bg-primary-600' }}
      {{ style == 'secondary' ? 'bg-orange-500 hover:bg-orange-600' }}
      {{ style == 'tertiary' ? 'bg-tertiary-500 hover:bg-tertiary-600' }}
      {{ style == 'darkblue' ? 'bg-secondary-500 hover:bg-secondary-600' }}
      shadow-lg
      hover:shadow-md
      offerLink
      {{ fullWidth == 'yes' ? 'block w-full' : 'inline-block md:inline-flex items-center md:w-auto md:text-left' }}"
    @click="formFocus = true"
    >
    {% if icon %}<i class="fa-solid fa-{{ icon }} mr-4 leading-none text-white {{ style == 'tertiary' ? 'text-primary-700' }}"></i>{% endif %}
    {{ text }}
</button>

{% endif %}
