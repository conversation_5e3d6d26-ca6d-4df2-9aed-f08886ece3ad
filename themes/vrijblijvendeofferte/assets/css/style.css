*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.18 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e5e5;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Barlow, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #a3a3a3;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #a3a3a3;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

a {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px) {
  h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 576px) {
  .container {
    max-width: 576px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 992px;
  }
}

@media (min-width: 1170px) {
  .container {
    max-width: 1170px;
  }
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: rgb(17 24 39 / 10%);
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: rgb(255 255 255 / 10%);
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sm {
  font-size: 0.875rem;
  line-height: 1.7142857;
}

.prose-sm :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-sm :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.1111111em;
}

.prose-sm :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}

.prose-sm :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}

.prose-sm :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}

.prose-sm :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}

.prose-sm :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-inline-end: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-inline-start: 0.3571429em;
}

.prose-sm :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
}

.prose-sm :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-sm :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}

.prose-sm :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}

.prose-sm :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}

.prose-sm :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}

.prose-sm :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}

.prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-lg {
  font-size: 1.125rem;
  line-height: 1.7777778;
}

.prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}

.prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2222222em;
  line-height: 1.4545455;
  margin-top: 1.0909091em;
  margin-bottom: 1.0909091em;
}

.prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  padding-inline-start: 1em;
}

.prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.6666667em;
  margin-top: 0;
  margin-bottom: 0.8333333em;
  line-height: 1;
}

.prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.6666667em;
  margin-top: 1.8666667em;
  margin-bottom: 1.0666667em;
  line-height: 1.3333333;
}

.prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.5;
}

.prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}

.prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}

.prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}

.prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}

.prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  border-radius: 0.3125rem;
  padding-top: 0.2222222em;
  padding-inline-end: 0.4444444em;
  padding-bottom: 0.2222222em;
  padding-inline-start: 0.4444444em;
}

.prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}

.prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8666667em;
}

.prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.875em;
}

.prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.75;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.375rem;
  padding-top: 1em;
  padding-inline-end: 1.5em;
  padding-bottom: 1em;
  padding-inline-start: 1.5em;
}

.prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.5555556em;
}

.prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.5555556em;
}

.prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
}

.prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4444444em;
}

.prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4444444em;
}

.prose-lg :where(.prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-lg :where(.prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-lg :where(.prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-lg :where(.prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-lg :where(.prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}

.prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  padding-inline-start: 1.5555556em;
}

.prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 3.1111111em;
  margin-bottom: 3.1111111em;
}

.prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
}

.prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  padding-inline-start: 0.75em;
}

.prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.75em;
  padding-inline-end: 0.75em;
  padding-bottom: 0.75em;
  padding-inline-start: 0.75em;
}

.prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}

.prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
  margin-top: 1em;
}

.prose-lg :where(.prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(.prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-xl {
  font-size: 1.25rem;
  line-height: 1.8;
}

.prose-xl :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2em;
  line-height: 1.5;
  margin-top: 1em;
  margin-bottom: 1em;
}

.prose-xl :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1.0666667em;
}

.prose-xl :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.8em;
  margin-top: 0;
  margin-bottom: 0.8571429em;
  line-height: 1;
}

.prose-xl :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.8em;
  margin-top: 1.5555556em;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose-xl :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.5em;
  margin-top: 1.6em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}

.prose-xl :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.8em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose-xl :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  border-radius: 0.3125rem;
  padding-top: 0.25em;
  padding-inline-end: 0.4em;
  padding-bottom: 0.25em;
  padding-inline-start: 0.4em;
}

.prose-xl :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8611111em;
}

.prose-xl :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.7777778;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.5rem;
  padding-top: 1.1111111em;
  padding-inline-end: 1.3333333em;
  padding-bottom: 1.1111111em;
  padding-inline-start: 1.3333333em;
}

.prose-xl :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  margin-bottom: 0.6em;
}

.prose-xl :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(.prose-xl > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(.prose-xl > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8em;
  margin-bottom: 2.8em;
}

.prose-xl :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
}

.prose-xl :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.8888889em;
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
  margin-top: 1em;
}

.prose-xl :where(.prose-xl > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(.prose-xl > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sky {
  --tw-prose-links: #0284c7;
  --tw-prose-invert-links: #0ea5e9;
}

.prose-primary {
  --tw-prose-body: #262626;
  --tw-prose-headings: #262626;
  --tw-prose-lead: #525252;
  --tw-prose-links: rgba(var(--color-primary), <alpha-value>);
  --tw-prose-bold: #262626;
  --tw-prose-counters: #525252;
  --tw-prose-bullets: rgba(var(--color-primary-400), <alpha-value>);
  --tw-prose-hr: rgba(var(--color-primary-300), <alpha-value>);
  --tw-prose-quotes: #262626;
  --tw-prose-quote-borders: rgba(var(--color-primary-300), <alpha-value>);
  --tw-prose-captions: rgba(var(--color-primary-700), <alpha-value>);
  --tw-prose-code: #262626;
  --tw-prose-pre-code: rgba(var(--color-primary-100), <alpha-value>);
  --tw-prose-pre-bg: #262626;
  --tw-prose-th-borders: rgba(var(--color-primary-300), <alpha-value>);
  --tw-prose-td-borders: rgba(var(--color-primary-200), <alpha-value>);
  --tw-prose-invert-body: rgba(var(--color-primary-200), <alpha-value>);
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: rgba(var(--color-primary-300), <alpha-value>);
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: rgba(var(--color-primary-400), <alpha-value>);
  --tw-prose-invert-bullets: rgba(var(--color-primary-600), <alpha-value>);
  --tw-prose-invert-hr: rgba(var(--color-primary-700), <alpha-value>);
  --tw-prose-invert-quotes: rgba(var(--color-primary-100), <alpha-value>);
  --tw-prose-invert-quote-borders: rgba(var(--color-primary-700), <alpha-value>);
  --tw-prose-invert-captions: rgba(var(--color-primary-400), <alpha-value>);
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: rgba(var(--color-primary-300), <alpha-value>);
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: rgba(var(--color-primary-600), <alpha-value>);
  --tw-prose-invert-td-borders: rgba(var(--color-primary-700), <alpha-value>);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-14 {
  bottom: -3.5rem;
}

.-bottom-8 {
  bottom: -2rem;
}

.-left-12 {
  left: -3rem;
}

.-left-8 {
  left: -2rem;
}

.-right-9 {
  right: -2.25rem;
}

.-top-1\/2 {
  top: -50%;
}

.-top-12 {
  top: -3rem;
}

.-top-4 {
  top: -1rem;
}

.-top-8 {
  top: -2rem;
}

.-top-\[2px\] {
  top: -2px;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-2 {
  bottom: 0.5rem;
}

.bottom-3 {
  bottom: 0.75rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-8 {
  bottom: 2rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-16 {
  left: 4rem;
}

.left-8 {
  left: 2rem;
}

.left-full {
  left: 100%;
}

.right-0 {
  right: 0px;
}

.right-1\/3 {
  right: 33.333333%;
}

.right-11 {
  right: 2.75rem;
}

.right-14 {
  right: 3.5rem;
}

.right-2 {
  right: 0.5rem;
}

.right-32 {
  right: 8rem;
}

.right-4 {
  right: 1rem;
}

.right-5 {
  right: 1.25rem;
}

.right-6 {
  right: 1.5rem;
}

.right-8 {
  right: 2rem;
}

.right-full {
  right: 100%;
}

.top-0 {
  top: 0px;
}

.top-1\.5 {
  top: 0.375rem;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.top-12 {
  top: 3rem;
}

.top-6 {
  top: 1.5rem;
}

.top-8 {
  top: 2rem;
}

.top-\[\.95rem\] {
  top: .95rem;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.z-\[101\] {
  z-index: 101;
}

.z-\[2\] {
  z-index: 2;
}

.z-\[300\] {
  z-index: 300;
}

.z-\[49\] {
  z-index: 49;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-end-7 {
  grid-column-end: 7;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.-m-3 {
  margin: -0.75rem;
}

.-m-4 {
  margin: -1rem;
}

.m-1 {
  margin: 0.25rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.-mx-8 {
  margin-left: -2rem;
  margin-right: -2rem;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx-0\.5 {
  margin-left: 0.125rem;
  margin-right: 0.125rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-24 {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.-mb-px {
  margin-bottom: -1px;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.-ml-px {
  margin-left: -1px;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.-mr-48 {
  margin-right: -12rem;
}

.-mt-1 {
  margin-top: -0.25rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.-mt-6 {
  margin-top: -1.5rem;
}

.-mt-7 {
  margin-top: -1.75rem;
}

.-mt-px {
  margin-top: -1px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-11 {
  margin-bottom: 2.75rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-1\.5 {
  margin-left: 0.375rem;
}

.ml-16 {
  margin-left: 4rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-11 {
  margin-top: 2.75rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-14 {
  margin-top: 3.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-auto {
  margin-top: auto;
}

.\!block {
  display: block !important;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-\[5\/4\] {
  aspect-ratio: 5/4;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-1\/2 {
  height: 50%;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-2 {
  height: 0.5rem;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-3 {
  height: 0.75rem;
}

.h-32 {
  height: 8rem;
}

.h-36 {
  height: 9rem;
}

.h-4 {
  height: 1rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-52 {
  height: 13rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-96 {
  height: 24rem;
}

.h-\[\.5rem\] {
  height: .5rem;
}

.h-\[1200px\] {
  height: 1200px;
}

.h-\[31px\] {
  height: 31px;
}

.h-\[81px\] {
  height: 81px;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.max-h-9 {
  max-height: 2.25rem;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[40px\] {
  max-height: 40px;
}

.max-h-\[530px\] {
  max-height: 530px;
}

.max-h-\[600px\] {
  max-height: 600px;
}

.max-h-full {
  max-height: 100%;
}

.min-h-\[150px\] {
  min-height: 150px;
}

.min-h-\[160px\] {
  min-height: 160px;
}

.min-h-\[220px\] {
  min-height: 220px;
}

.min-h-\[356px\] {
  min-height: 356px;
}

.min-h-screen {
  min-height: 100vh;
}

.\!w-full {
  width: 100% !important;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/4 {
  width: 25%;
}

.w-1\/6 {
  width: 16.666667%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-18 {
  width: 4.5rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-2\/6 {
  width: 33.333333%;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\/4 {
  width: 75%;
}

.w-3\/5 {
  width: 60%;
}

.w-32 {
  width: 8rem;
}

.w-36 {
  width: 9rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-52 {
  width: 13rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[\.5rem\] {
  width: .5rem;
}

.w-\[1200px\] {
  width: 1200px;
}

.w-\[170px\] {
  width: 170px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[22\%\] {
  width: 22%;
}

.w-\[27\%\] {
  width: 27%;
}

.w-\[30\%\] {
  width: 30%;
}

.w-\[33\%\] {
  width: 33%;
}

.w-\[43\%\] {
  width: 43%;
}

.w-\[45\%\] {
  width: 45%;
}

.w-\[9999px\] {
  width: 9999px;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[70\%\] {
  max-width: 70%;
}

.max-w-\[800px\] {
  max-width: 800px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-prose {
  max-width: 65ch;
}

.max-w-screen-2xl {
  max-width: 1536px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.flex-grow-0 {
  flex-grow: 0;
}

.grow {
  flex-grow: 1;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/3 {
  --tw-translate-x: -33.333333%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/4 {
  --tw-translate-x: -25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-16 {
  --tw-translate-x: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-4 {
  --tw-translate-x: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/3 {
  --tw-translate-y: -33.333333%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-6 {
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[14px\] {
  --tw-translate-y: -14px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/3 {
  --tw-translate-x: 33.333333%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-7 {
  --tw-translate-x: 1.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[29px\] {
  --tw-translate-x: 29px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-16 {
  --tw-translate-y: 4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-180 {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-\[4deg\] {
  --tw-rotate: -4deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-skew-x-\[29deg\] {
  --tw-skew-x: -29deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[55\%\] {
  --tw-scale-x: 55%;
  --tw-scale-y: 55%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-x-\[-1\] {
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-y-\[-1\] {
  --tw-scale-y: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes slide {
  0%, 100% {
    left: 0px;
  }

  50% {
    left: -6px;
  }
}

.animate-slide {
  animation: slide 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-col-resize {
  cursor: col-resize;
}

.cursor-move {
  cursor: move;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-row-resize {
  cursor: row-resize;
}

.resize {
  resize: both;
}

.scroll-mt-4 {
  scroll-margin-top: 1rem;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-\[230px_1fr\] {
  grid-template-columns: 230px 1fr;
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}

.gap-x-1\.5 {
  -moz-column-gap: 0.375rem;
       column-gap: 0.375rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-divide-opacity, 1));
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-divide-opacity, 1));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-pretty {
  text-wrap: pretty;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-l {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-tl-none {
  border-top-left-radius: 0px;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-\[3px\] {
  border-width: 3px;
}

.border-x {
  border-left-width: 1px;
  border-right-width: 1px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-4 {
  border-bottom-width: 4px;
}

.border-l {
  border-left-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.\!border-gray-300 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1)) !important;
}

.\!border-transparent {
  border-color: transparent !important;
}

.\!border-yellow-400 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1)) !important;
}

.border-apple-800 {
  --tw-border-opacity: 1;
  border-color: rgb(14 111 12 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}

.border-gray-200\/50 {
  border-color: rgb(229 229 229 / 0.5);
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}

.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));
}

.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--tw-border-opacity, 1));
}

.border-lime-500 {
  --tw-border-opacity: 1;
  border-color: rgb(132 204 22 / var(--tw-border-opacity, 1));
}

.border-primary-200 {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-200), var(--tw-border-opacity, 1));
}

.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
}

.border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-600), var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-secondary-500 {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-secondary), var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity, 1));
}

.\!border-b-transparent {
  border-bottom-color: transparent !important;
}

.border-b-black\/30 {
  border-bottom-color: rgb(0 0 0 / 0.3);
}

.border-b-blue-700 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.border-b-gray-300 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-b-white {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-l-gray-300 {
  --tw-border-opacity: 1;
  border-left-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-r-gray-300 {
  --tw-border-opacity: 1;
  border-right-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: transparent;
}

.\!bg-yellow-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#F5F5F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.bg-\[\#c7c7c7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(199 199 199 / var(--tw-bg-opacity, 1));
}

.bg-\[\#e0f8f4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(224 248 244 / var(--tw-bg-opacity, 1));
}

.bg-\[\#ff8a00\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 0 / var(--tw-bg-opacity, 1));
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}

.bg-apple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(10 183 4 / var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/5 {
  background-color: rgb(0 0 0 / 0.05);
}

.bg-black\/70 {
  background-color: rgb(0 0 0 / 0.7);
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.bg-gray-200\/30 {
  background-color: rgb(229 229 229 / 0.3);
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(115 115 115 / var(--tw-bg-opacity, 1));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(154 196 77 / var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(123 157 62 / var(--tw-bg-opacity, 1));
}

.bg-indigo-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-indigo-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 48 163 / var(--tw-bg-opacity, 1));
}

.bg-lime-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity, 1));
}

.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity, 1));
}

.bg-logo-orange {
  --tw-bg-opacity: 1;
  background-color: rgb(217 129 45 / var(--tw-bg-opacity, 1));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.bg-orange-200\/70 {
  background-color: rgb(254 215 170 / 0.7);
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-100), var(--tw-bg-opacity, 1));
}

.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-600), var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.bg-red-200\/70 {
  background-color: rgb(254 202 202 / 0.7);
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-secondary-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary-100), var(--tw-bg-opacity, 1));
}

.bg-secondary-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.bg-secondary-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary-600), var(--tw-bg-opacity, 1));
}

.bg-secondary-700\/80 {
  background-color: rgba(var(--color-secondary-700), 0.8);
}

.bg-secondary-800 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary-800), var(--tw-bg-opacity, 1));
}

.bg-sky-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}

.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
}

.bg-tertiary-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-tertiary), var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}

.bg-opacity-\[15\%\] {
  --tw-bg-opacity: 15%;
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.bg-none {
  background-image: none;
}

.from-black\/70 {
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.bg-cover {
  background-size: cover;
}

.bg-bottom {
  background-position: bottom;
}

.bg-center {
  background-position: center;
}

.bg-left {
  background-position: left;
}

.bg-left-bottom {
  background-position: left bottom;
}

.bg-left-top {
  background-position: left top;
}

.bg-right {
  background-position: right;
}

.bg-right-bottom {
  background-position: right bottom;
}

.bg-right-top {
  background-position: right top;
}

.bg-top {
  background-position: top;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-white {
  fill: #fff;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-bottom {
  -o-object-position: bottom;
     object-position: bottom;
}

.object-left-bottom {
  -o-object-position: left bottom;
     object-position: left bottom;
}

.p-1 {
  padding: 0.25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-24 {
  padding-left: 6rem;
  padding-right: 6rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.\!pb-24 {
  padding-bottom: 6rem !important;
}

.\!pr-4 {
  padding-right: 1rem !important;
}

.\!pr-6 {
  padding-right: 1.5rem !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pr-0 {
  padding-right: 0px;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-9 {
  padding-top: 2.25rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.font-roboto {
  font-family: Roboto, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.font-sans {
  font-family: Barlow, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.text-9xl {
  font-size: 8rem;
  line-height: 1;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-lgr {
  font-size: 1.375rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.not-italic {
  font-style: normal;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.\!text-gray-300 {
  --tw-text-opacity: 1 !important;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-500 {
  --tw-text-opacity: 1 !important;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#6a6cf7\] {
  --tw-text-opacity: 1;
  color: rgb(106 108 247 / var(--tw-text-opacity, 1));
}

.text-\[\#e9ecef\] {
  --tw-text-opacity: 1;
  color: rgb(233 236 239 / var(--tw-text-opacity, 1));
}

.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.text-apple-700 {
  --tw-text-opacity: 1;
  color: rgb(10 142 7 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-body {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(154 196 77 / var(--tw-text-opacity, 1));
}

.text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity, 1));
}

.text-lime-600 {
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity, 1));
}

.text-lime-800 {
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity, 1));
}

.text-lime-900 {
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-primary-200 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-200), var(--tw-text-opacity, 1));
}

.text-primary-400 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-400), var(--tw-text-opacity, 1));
}

.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-700), var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.text-review {
  --tw-text-opacity: 1;
  color: rgb(250 176 23 / var(--tw-text-opacity, 1));
}

.text-secondary-100 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary-100), var(--tw-text-opacity, 1));
}

.text-secondary-500 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary), var(--tw-text-opacity, 1));
}

.text-secondary-600 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary-600), var(--tw-text-opacity, 1));
}

.text-secondary-800 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary-800), var(--tw-text-opacity, 1));
}

.text-sky-500 {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity, 1));
}

.text-sky-600 {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}

.text-sky-900 {
  --tw-text-opacity: 1;
  color: rgb(12 74 110 / var(--tw-text-opacity, 1));
}

.text-social-whatsapp {
  --tw-text-opacity: 1;
  color: rgb(37 211 102 / var(--tw-text-opacity, 1));
}

.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}

.text-vbo-orange {
  --tw-text-opacity: 1;
  color: rgb(233 100 16 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity, 1));
}

.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

.caret-secondary-500 {
  caret-color: rgba(var(--color-secondary), 1);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-75 {
  opacity: 0.75;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-form {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1), 0 4px 28px 0px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color), 0 4px 28px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-black\/20 {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-black\/5 {
  --tw-shadow-color: rgb(0 0 0 / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-\[4px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}

.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.ring-yellow-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 224 71 / var(--tw-ring-opacity, 1));
}

.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-\[0_20px_13px_rgba\(0\2c 0\2c 0\2c 0\.3\)\] {
  --tw-drop-shadow: drop-shadow(0 20px 13px rgba(0,0,0,0.3));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-saturate-50 {
  --tw-backdrop-saturate: saturate(.5);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-\[4s\] {
  transition-delay: 4s;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-75 {
  transition-duration: 75ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

[x-cloak] {
  display: none !important;
}

.prose-primary a {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.prose-primary a:hover {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.prose-primary li::marker {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.basic_content p:not(:last-of-type) {
  margin-bottom: 1rem;
}

.sticker-big {
  display: block;
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  .sticker-big {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .sticker-big {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

@media (min-width: 1280px) {
  .sticker-big {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.swiper-pagination-bullet {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
  opacity: 0.3;
}

.swiper-pagination-bullet-active {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
  opacity: 1;
}

.review-slider {
  width: 100%;
  height: 100%;
}

.review-slider .swiper-slide {
  display: flex;
  width: 100%;
  height: auto;
}

.form-hor-wrap {
  position: relative;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .form-hor-wrap {
    margin-bottom: 2rem;
  }
}

.form-hor-wrap-sm {
  position: relative;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .form-hor-wrap-sm {
    margin-bottom: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .form-hor-wrap-sm {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

.field-section {
  position: relative;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .field-section {
    margin-bottom: 2rem;
  }
}

.with-lines .form-hor-wrap:not(:last-child) {
  position: relative;
  margin-bottom: 0.5rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
  padding-bottom: 0.5rem;
}

@media (min-width: 1024px) {
  .with-lines .form-hor-wrap:not(:last-child) {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
  }
}

.with-lines .form-hor-wrap-sm:not(:last-child) {
  position: relative;
  margin-bottom: 0.5rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
  padding-bottom: 0.5rem;
}

@media (min-width: 1024px) {
  .with-lines .form-hor-wrap-sm:not(:last-child) {
    margin-bottom: 1rem;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-bottom: 1rem;
  }
}

@media (min-width: 1280px) {
  .with-lines .form-hor-wrap-sm:not(:last-child) {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

.form-wide .form-hor-wrap-sm, .form-hor-wrap-sm.wide {
  position: relative;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .form-wide .form-hor-wrap-sm, .form-hor-wrap-sm.wide {
    margin-bottom: 2rem;
    padding-left: 0px;
    padding-right: 0px;
  }
}

@media (min-width: 1280px) {
  .form-wide .form-hor-wrap-sm, .form-hor-wrap-sm.wide {
    padding-left: 0px;
    padding-right: 0px;
  }
}

.form-hor-wrap.form-group-slider {
  position: relative;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .form-hor-wrap.form-group-slider {
    margin-bottom: 1rem;
  }
}

.form-hor-wrap .help-block.form-field-error-label {
  margin-top: 0.25rem;
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

@media (min-width: 1024px) {
  .form-hor-wrap .help-block.form-field-error-label {
    margin-left: auto;
    width: 50%;
    padding-left: 0.5rem;
  }
}

.form-hor-wrap-sm .help-block.form-field-error-label {
  margin-top: 0.25rem;
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

@media (min-width: 1024px) {
  .form-hor-wrap-sm .help-block.form-field-error-label {
    margin-left: auto;
    width: 66.666667%;
    padding-left: 0.5rem;
  }
}

.form-hor-label {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.form-hor-label > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.form-hor-label {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@media (min-width: 768px) {
  .form-hor-label {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .form-hor-label {
    margin-bottom: 0px;
  }
}

.form-hor-label.invalid {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.form-hor-label svg {
  margin-left: 0.5rem;
}

.form-hor-field {
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.375rem;
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.form-hor-field::-moz-placeholder {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-hor-field::placeholder {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-hor-field:focus {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(64 64 64 / var(--tw-ring-opacity, 1));
}

.form-hor-field.invalid {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.form-hor-field.field-invalid {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.form-hor-field.validate:invalid {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.form-hor-field.validate:invalid:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1));
}

/* .form-hor-field.valid { @apply ring-2 ring-lime-500 focus:valid:ring-lime-600; }
.form-hor-field.validate { @apply valid:ring-2 valid:ring-lime-500 focus:valid:ring-lime-600 invalid:ring-2 invalid:ring-red-500 focus:invalid:ring-red-600; } */

[data-name="zipcode"] {
  text-transform: uppercase;
}

.validation-icon-valid {
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  display: none;
  align-items: center;
  padding-right: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity, 1));
}

.peer:valid ~ .validation-icon-valid {
  display: flex;
}

.validation-icon-invalid {
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  display: none;
  align-items: center;
  padding-right: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.peer:invalid ~ .validation-icon-invalid {
  display: flex;
}

.form-hor-grid-3 {
  position: relative;
  align-items: center;
}

@media (min-width: 768px) {
  .form-hor-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
  }
}

.form-hor-grid-2 {
  position: relative;
  align-items: center;
}

@media (min-width: 768px) {
  .form-hor-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
  }
}

.form-hor-cell-1 {
  position: relative;
  grid-column: span 1 / span 1;
}

.form-hor-cell-2 {
  position: relative;
  grid-column: span 2 / span 2;
}

.form-hor-cell-3 {
  position: relative;
  grid-column: span 3 / span 3;
}

@media (min-width: 768px) {
  .form-hor-grid-2 > div:last-child,
.form-hor-grid-3 > div:last-child {
    padding-left: 0.5rem;
  }
}

.form-hor-wrap-sm.wide .form-hor-grid-3 {
  position: relative;
  align-items: center;
}

@media (min-width: 768px) {
  .form-hor-wrap-sm.wide .form-hor-grid-3 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
  }
}

.form-hor-wrap-sm.wide .form-hor-grid-3 .form-hor-cell-2 {
  position: relative;
  grid-column: span 1 / span 1;
}

.form-hor-wrap-sm.wide.small .form-hor-grid-3 {
  position: relative;
  align-items: center;
}

@media (min-width: 768px) {
  .form-hor-wrap-sm.wide.small .form-hor-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
  }
}

.form-hor-wrap-sm.wide.small .form-hor-grid-3 .form-hor-cell-2 {
  position: relative;
  grid-column: span 2 / span 2;
}

@media (min-width: 1024px) {
  .form-hor-wrap-sm.wide.small {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .form-hor-wrap-sm.wide.small {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

.form-inner-grid-12 {
  position: relative;
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-inner-grid-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

.form-inner-grid-4 {
  position: relative;
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-inner-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.form-inner-grid-3 {
  position: relative;
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-inner-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.form-inner-cell-12 {
  position: relative;
  grid-column: span 2 / span 2;
}

@media (min-width: 768px) {
  .form-inner-cell-12 {
    grid-column: span 12 / span 12;
  }
}

.form-inner-cell-7 {
  position: relative;
  grid-column: span 1 / span 1;
}

@media (min-width: 768px) {
  .form-inner-cell-7 {
    grid-column: span 7 / span 7;
  }
}

.form-inner-cell-5 {
  position: relative;
  grid-column: span 1 / span 1;
}

@media (min-width: 768px) {
  .form-inner-cell-5 {
    grid-column: span 5 / span 5;
  }
}

.form-inner-cell-3 {
  position: relative;
  grid-column: span 3 / span 3;
}

.form-inner-cell-2 {
  position: relative;
  grid-column: span 3 / span 3;
}

@media (min-width: 768px) {
  .form-inner-cell-2 {
    grid-column: span 2 / span 2;
  }
}

.form-inner-cell-1 {
  position: relative;
  grid-column: span 2 / span 2;
}

@media (min-width: 768px) {
  .form-inner-cell-1 {
    grid-column: span 1 / span 1;
  }
}

.addressSelectorWrap .form-inner-grid-12 {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .addressSelectorWrap .form-inner-grid-12 {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

.addressSelectorWrap .form-inner-cell-12 {
  position: relative;
  width: 100%;
}

@media (min-width: 768px) {
  .addressSelectorWrap .form-inner-cell-12 {
    grid-column: span 12 / span 12;
  }
}

.addressSelectorWrap .form-inner-cell-7 {
  position: relative;
  flex: 1 1 auto;
}

@media (min-width: 768px) {
  .addressSelectorWrap .form-inner-cell-7 {
    grid-column: span 7 / span 7;
  }
}

.addressSelectorWrap .form-inner-cell-5 {
  position: relative;
  flex: 1 1 auto;
}

@media (min-width: 768px) {
  .addressSelectorWrap .form-inner-cell-5 {
    grid-column: span 5 / span 5;
  }
}

.form-hor-select-icon {
  pointer-events: none;
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  display: flex;
  align-items: center;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.form-hor-select-icon-svg {
  height: 1.5rem;
  width: 1.5rem;
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.form-hor-select-icon-svg.valid {
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity, 1));
}

.form-hor-radiolist > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.form-hor-radiolist .radio label {
  display: flex;
  align-items: center;
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.form-hor-radiolist .form-hor-radio {
  margin-right: 1rem;
  height: 1rem;
  width: 1rem;
  border-radius: 9999px;
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(38 38 38 / var(--tw-ring-opacity, 1));
}

.form-hor-radiolist .form-hor-radio:checked {
  border-color: transparent;
}

.form-hor-radiolist .form-hor-radio:hover:checked {
  border-color: transparent;
}

.form-hor-grid-radio {
  align-items: flex-start;
}

/* .form-hor-grid-radio .form-hor-label { @apply mt-1; } */

.form-hor-wrap-sm.wide .form-hor-grid-radio {
  align-items: flex-start;
}

.radio-float .form-hor-grid-radio {
  align-items: center !important;
}

.radio-float .form-hor-radiolist {
  display: flex;
  align-items: center;
}

.radio-float .form-hor-radiolist > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.radio-float .form-hor-radiolist .form-hor-radio {
  margin-right: 0.5rem;
}

.form-hor-checklist > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.form-hor-checklist .checkbox label {
  display: flex;
  align-items: center;
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.form-hor-checklist .form-hor-checkbox {
  margin-right: 1rem;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.form-hor-checklist .form-hor-checkbox:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(var(--color-primary), var(--tw-ring-opacity, 1));
}

.form-hor-grid-check, .form-hor-grid-textarea {
  align-items: flex-start;
}

.form-hor-grid-check .form-hor-label {
  margin-top: 0.25rem;
}

.form-hor-grid-range {
  align-items: flex-start;
}

.form-hor-range .form-hor-range-field {
  height: 0.25rem;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
  padding: 0px;
}

.form-hor-range .form-hor-range-field:focus {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.form-hor-range-value-overlay {
  pointer-events: none;
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: right;
  font-weight: 600;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.peer:focus ~ .form-hor-range-value-overlay {
  display: none;
}

.form-hor-range .form-hor-range-field::-webkit-slider-runnable-track {
  position: relative;
}

.form-hor-range .form-hor-range-field::-webkit-slider-thumb {
  position: relative;
  height: 1.25rem;
  width: 1.25rem;
  -webkit-appearance: none;
          appearance: none;
  border-radius: 9999px;
  border-width: 5px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-hor-range .form-hor-range-field::-webkit-slider-thumb::before {
  position: absolute;
  height: 0.5rem;
  width: 100%;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-200), var(--tw-bg-opacity, 1));
}

.group:hover .form-hor-range .form-hor-range-field::-webkit-slider-thumb {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-600), var(--tw-border-opacity, 1));
}

.form-hor-range .form-hor-range-field:focus::-webkit-slider-thumb {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-600), var(--tw-border-opacity, 1));
}

.form-hor-range .form-hor-range-field:active::-webkit-slider-thumb {
  border-width: 7px;
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-600), var(--tw-border-opacity, 1));
}

.form-hor-range .form-hor-range-field::-ms-fill-lower {
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.form-hor-range .form-hor-range-field::-ms-fill-upper {
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.form-hor-range .form-hor-range-field::-moz-range-thumb {
  position: relative;
  height: 1.25rem;
  width: 1.25rem;
  -moz-appearance: none;
       appearance: none;
  border-radius: 9999px;
  border-width: 5px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  -moz-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-hor-range .form-hor-range-field::-moz-range-thumb::before {
  position: absolute;
  height: 0.5rem;
  width: 100%;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-200), var(--tw-bg-opacity, 1));
}

.form-hor-range .form-hor-range-field::-moz-range-thumb:focus {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-600), var(--tw-border-opacity, 1));
}

.form-hor-range .form-hor-range-value {
  margin-top: 0.75rem;
  display: inline-block;
  width: 8rem;
  border-radius: 0.25rem;
  border-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: right;
  font-weight: 600;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.form-btn {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.form-btn:disabled {
  cursor: not-allowed;
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}

.form-btn span {
  line-height: 1;
}

.form-btn span svg {
  align-items: center;
  font-size: 1rem;
  line-height: 1.5rem;
}

.form-btn-prev {
  margin-top: 0.5rem;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-btn-prev:hover {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .form-btn-prev {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.with-lines .form-btn {
  position: relative;
  margin-top: 0.5rem;
}

.with-lines .form-btn::before {
  pointer-events: none;
  position: absolute;
  left: 0px;
  right: 0px;
  top: -0.5rem;
  width: 100%;
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-top-width: 1px;
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

@media (min-width: 1024px) {
  .with-lines .form-btn {
    margin-top: 1rem;
  }

  .with-lines .form-btn::before {
    content: var(--tw-content);
    top: -1rem;
  }
}

.steps-wrap {
  position: relative;
  margin: -1.5rem;
  margin-bottom: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding: 1.25rem;
}

@media (min-width: 768px) {
  .steps-wrap {
    margin: -3rem;
    margin-bottom: 2rem;
    padding-left: 3rem;
    padding-right: 3rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

.steps-wrap .steps {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.steps .step-bg-line {
  position: absolute;
  left: 1rem;
  right: 1rem;
  height: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

@media (min-width: 1024px) {
  .steps .step-bg-line {
    left: 3rem;
    right: 3rem;
  }
}

.steps .step {
  position: relative;
  z-index: 20;
  display: flex;
  height: 2.25rem;
  width: 2.25rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 2px;
  border-color: rgb(163 163 163 / 0.5);
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 245 245 / var(--tw-ring-opacity, 1));
}

@media (min-width: 768px) {
  .steps .step {
    height: 3.5rem;
    width: 3.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.steps .step.completed {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.steps .step.active, .steps .step.current.active {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.steps .step.current {
  z-index: 0;
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary-300), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-300), var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.steps .step.current::before {
  position: absolute;
  top: 50%;
  right: 100%;
  display: flex;
  height: 0.125rem;
  width: 999px;
  --tw-translate-y: -1px;
  --tw-translate-x: -0.625rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-400), var(--tw-bg-opacity, 1));
}

.form-fieldset {
  position: relative;
}

.form-fieldset:not(:first-of-type) {
  display: none;
}

.field-section.field-description {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.field-section.field-description :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.field-section.field-description :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.field-section.field-description :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.field-section.field-description :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.field-section.field-description :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.field-section.field-description :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.field-section.field-description :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.field-section.field-description :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.field-section.field-description :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.field-section.field-description :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.field-section.field-description :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.field-section.field-description :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.field-section.field-description :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.field-section.field-description :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.field-section.field-description :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.field-section.field-description :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.field-section.field-description :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.field-section.field-description :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.field-section.field-description :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.field-section.field-description :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.field-section.field-description :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.field-section.field-description :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.field-section.field-description :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.field-section.field-description :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}

.field-section.field-description :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.field-section.field-description :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.field-section.field-description :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.field-section.field-description :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.field-section.field-description :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.field-section.field-description :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.field-section.field-description :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.field-section.field-description :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.field-section.field-description :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.field-section.field-description :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.field-section.field-description :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.field-section.field-description :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.field-section.field-description :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.field-section.field-description :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.field-section.field-description :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.field-section.field-description :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.field-section.field-description :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.field-section.field-description :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.field-section.field-description :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.field-section.field-description :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.field-section.field-description :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.field-section.field-description :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.field-section.field-description :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.field-section.field-description :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.field-section.field-description :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.field-section.field-description :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.field-section.field-description :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.field-section.field-description :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.field-section.field-description :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.field-section.field-description :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.field-section.field-description :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.field-section.field-description {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: rgb(17 24 39 / 10%);
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: rgb(255 255 255 / 10%);
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.field-section.field-description :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.field-section.field-description :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.field-section.field-description :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.field-section.field-description :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.field-section.field-description :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.field-section.field-description :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.field-section.field-description :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.field-section.field-description :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.field-section.field-description :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.field-section.field-description :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.field-section.field-description :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.field-section.field-description :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.field-section.field-description :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.field-section.field-description :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.field-section.field-description :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.field-section.field-description :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.field-section.field-description :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.field-section.field-description :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.field-section.field-description :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.field-section.field-description :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.field-section.field-description :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.field-section.field-description :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.field-section.field-description :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.field-section.field-description :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.field-section.field-description {
  margin-bottom: 2rem;
}

.field-section.field-description span strong,.field-section.field-description span i {
  color: inherit;
}

@media (min-width: 768px) {
  .field-section.field-description {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .field-section.field-description :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .field-section.field-description :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .field-section.field-description :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-inline-start: 1em;
  }

  .field-section.field-description :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .field-section.field-description :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.6666667em;
    margin-top: 1.8666667em;
    margin-bottom: 1.0666667em;
    line-height: 1.3333333;
  }

  .field-section.field-description :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.5;
  }

  .field-section.field-description :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .field-section.field-description :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .field-section.field-description :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .field-section.field-description :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .field-section.field-description :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .field-section.field-description :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-inline-end: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-inline-start: 0.4444444em;
  }

  .field-section.field-description :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .field-section.field-description :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .field-section.field-description :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .field-section.field-description :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-inline-end: 1.5em;
    padding-bottom: 1em;
    padding-inline-start: 1.5em;
  }

  .field-section.field-description :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .field-section.field-description :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .field-section.field-description :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .field-section.field-description :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .field-section.field-description :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .field-section.field-description :where(.md\:prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .field-section.field-description :where(.md\:prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .field-section.field-description :where(.md\:prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .field-section.field-description :where(.md\:prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .field-section.field-description :where(.md\:prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .field-section.field-description :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .field-section.field-description :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .field-section.field-description :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .field-section.field-description :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-inline-start: 1.5555556em;
  }

  .field-section.field-description :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .field-section.field-description :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .field-section.field-description :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .field-section.field-description :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .field-section.field-description :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .field-section.field-description :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .field-section.field-description :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .field-section.field-description :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .field-section.field-description :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .field-section.field-description :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .field-section.field-description :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .field-section.field-description :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .field-section.field-description :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .field-section.field-description :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .field-section.field-description :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .field-section.field-description :where(.md\:prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .field-section.field-description :where(.md\:prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}

.field-section.field-description :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-weight: 700;
}

.field-section.field-description :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.newsletter-form {
  align-items: center;
}

.newsletter-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  .newsletter-form {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 1rem;
  }

  .newsletter-form > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .newsletter-field-wrap {
    grid-column: span 5 / span 5;
  }

  .newsletter-zipcode-wrap {
    grid-column: span 3 / span 3;
  }

  .newsletter-submit-wrap {
    grid-column: span 4 / span 4;
  }
}

.newsletter-field {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 2px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.newsletter-field:focus {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.newsletter-submit {
  display: block;
  width: 100%;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 0.375rem;
  border-width: 2px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  text-align: center;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.newsletter-submit:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-600), var(--tw-bg-opacity, 1));
}

@media (min-width: 768px) {
  .newsletter-submit {
    display: inline-flex;
    width: auto;
    text-align: left;
  }

  #section_thankyou .newsletter-field-wrap {
    grid-column: span 6 / span 6;
  }

  #section_thankyou .newsletter-zipcode-wrap {
    grid-column: span 3 / span 3;
  }

  #section_thankyou .newsletter-submit-wrap {
    grid-column: span 3 / span 3;
  }
}

#section_thankyou .newsletter-submit {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
}

#section_thankyou iframe {
  width: 100% !important;
}

.alert {
  margin-bottom: 0.75rem;
  display: inline-flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.375rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-weight: 500;
}

.alert-success {
  --tw-bg-opacity: 1;
  background-color: rgb(154 196 77 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.alert-default {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.alert-error {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.alert .close {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1;
}

.alert-info {
  display: block;
  --tw-bg-opacity: 1;
  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));
}

.custom_heading h1,
.custom_heading h2,
.custom_heading h3,
.custom_heading h4 {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.custom_heading h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 768px) {
  .custom_heading h1 {
    font-size: 3rem;
    line-height: 1;
  }
}

.form_prelander form .custom_heading h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 768px) {
  .form_prelander form .custom_heading h1 {
    font-size: 2.5rem;
  }
}

.custom_heading h2 {
  margin-bottom: 0px;
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  .custom_heading h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.custom_heading h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  .custom_heading h3 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

.custom_heading h4 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  .custom_heading h4 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.custom_heading p {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  .custom_heading p {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.list-slider-nav .swiper-slide-thumb-active {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.list-slider-nav .swiper-slide-thumb-active:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.suggestZipcode {
  position: absolute;
  top: 4.5rem;
  left: 0px;
  z-index: 5;
  width: 100%;
  list-style-type: none;
  overflow-y: auto;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.suggestZipcodeItem {
  cursor: pointer;
  padding: 0.5rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.suggestZipcodeItem:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

@media (min-width: 768px) {
  .suggestZipcodeItem {
    padding: 0.25rem;
  }
}

.prose span strong, .prose span i {
  color: inherit;
}

.form-hor-icon-calendar {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.form-group:not(.date_field) #geboortedatum {
  padding-left: 2.5rem;
}

.form-hor-field-suggestions {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

[list]::-webkit-calendar-picker-indicator {
  visibility: hidden;
  display: none;
  opacity: 0;
}

input::-webkit-calendar-picker-indicator {
  visibility: hidden;
  display: none;
  opacity: 0;
}

.grid-dob {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 1.5rem;
}

.grid-dob > .grid-dob-col-year {
  grid-column: span 2 / span 2;
}

.form-group-dob .form-hor-field {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.dobComment {
  margin-top: 0.5rem;
  display: none;
  text-align: right;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.form-group.label-sm .form-hor-label {
  font-size: 1rem;
  line-height: 1.5rem;
}

.form-group.label-xs .form-hor-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* .field-section { @apply mb-3; } */

.field-section-description {
  margin-top: 2rem;
}

.field-section-description p {
  line-height: 1rem;
}

.field-section.field-description h1:first-child,
.field-section.field-description h2:first-child,
.field-section.field-description h3:first-child,
.field-section.field-description h4:first-child,
.field-section.field-description p:first-child {
  margin-top: 0 !important;
}

/* Prelander */

.form_prelander form .form-hor-grid-3 {
  position: relative;
}

@media (min-width: 768px) {
  .form_prelander form .form-hor-grid-3 {
    display: block;
  }
}

.form_prelander form .form-hor-grid-2 {
  position: relative;
}

@media (min-width: 768px) {
  .form_prelander form .form-hor-grid-2 {
    display: block;
  }
}

.form_prelander form .form-hor-wrap-sm {
  padding-left: 0px;
  padding-right: 0px;
}

.form_prelander form .steps-wrap {
  position: relative;
  margin: -1.5rem;
  margin-bottom: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  padding: 1.25rem;
}

@media (min-width: 768px) {
  .form_prelander form .steps-wrap {
    margin: -3rem;
    margin-bottom: 2rem;
    padding-left: 3rem;
    padding-right: 3rem;
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }
}

.form_prelander form .steps-wrap .steps {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.form_prelander form .steps .step-bg-line {
  position: absolute;
  left: 3rem;
  right: 3rem;
  height: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.form_prelander form .steps .step {
  height: 2.25rem;
  width: 2.25rem;
}

@media (min-width: 768px) {
  .form_prelander form .steps .step {
    height: 2.25rem;
    width: 2.25rem;
  }
}

.form_prelander form .form-hor-label {
  margin-bottom: 0.75rem;
  display: block;
}

.form_prelander form .form-hor-radiolist {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 1rem;
}

.form_prelander form .form-hor-radiolist > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.form_prelander form .form-hor-radiolist .radio-label {
  height: 100%;
  width: 100%;
}

.form_prelander form .form-hor-radiolist .radio-label-inner {
  display: flex;
  height: 100%;
  width: 100%;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-primary), var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  text-align: center;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1.25;
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.form_prelander form .form-hor-radiolist .radio-label-inner.long-label {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.form_prelander form .form-hor-radiolist .radio-label-inner.short-label {
  padding-left: 1rem;
  padding-right: 1rem;
}

.form_prelander form .form-hor-radiolist .radio-label input:checked ~ .radio-label-inner {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.icon-animate-slide svg {
  position: relative;
}

@keyframes slide {
  0%, 100% {
    left: 0px;
  }

  50% {
    left: -6px;
  }
}

.icon-animate-slide svg {
  animation: slide 1s ease-in-out infinite;
}

.icon-animate-wiggle svg {
  position: relative;
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(-8deg);
  }

  50% {
    transform: rotate(8deg);
  }
}

.icon-animate-wiggle svg {
  animation: wiggle 1s ease-in-out infinite;
}

.icon-animate-spin svg {
  position: relative;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.icon-animate-spin svg {
  animation: spin 1s linear infinite;
}

.bruto-maandinkomen,
.bruto-maandinkomen_partner,
.pensioeninkomen-incl,
.pensioeninkomen-excl,
.pensioeninkomen-incl_partner,
.pensioeninkomen-excl_partner,
.broninkomen,
.broninkomen_partner {
  display: none;
}

.sup-onder sup {
  display: block;
  top: 8px;
}

.wrapper-int-phone .iti {
  width: 100%;
}

/* Boxes */

.oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
  padding-top: 2rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
    padding-top: 4rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
    padding-top: 6rem;
  }
}

.oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
    padding-bottom: 6rem;
  }
}

.oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
  padding-top: 1rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
    padding-top: 2rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
    padding-top: 3rem;
  }
}

.oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
  padding-bottom: 1rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
    padding-bottom: 2rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
    padding-bottom: 3rem;
  }
}

.oc-boxes-spacing--before-small    [data-rel="boxes-wrapper"] {
  padding-top: 2rem;
}

.oc-boxes-spacing--after-small     [data-rel="boxes-wrapper"] {
  padding-bottom: 2rem;
}

.oc-boxes-spacing--before-none     [data-rel="boxes-wrapper"] {
  padding-top: 0px;
}

.oc-boxes-spacing--after-none      [data-rel="boxes-wrapper"] {
  padding-bottom: 0px;
}

.oc-boxes-edit-mode header.sticky {
  position: static;
}

.card-same-height .oc-box--child-card,
.card-same-height .oc-box--child-card .card,
.card-same-height .oc-box--child-image,
.card-same-height .oc-box--child-image .image-wrapper {
  height: 100%;
}

.card-same-height .image-wrapper img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

#cookies-bar {
  position: fixed;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 50;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 768px) {
  #cookies-bar {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

#cookies-bar {
  box-shadow: 0 -20px 25px -5px rgba(0,0,0,.05), 0 -8px 10px -6px rgba(0,0,0,.05);
}

#cookies-bar .cookies-bar {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  row-gap: 1rem;
}

@media (min-width: 768px) {
  #cookies-bar .cookies-bar {
    flex-wrap: nowrap;
    gap: 2.5rem;
  }
}

#cookies-bar .cookiebar-icon {
  display: none;
  width: 25%;
}

@media (min-width: 768px) {
  #cookies-bar .cookiebar-icon {
    display: flex;
    width: auto;
  }
}

#cookies-bar .cookiebar-content {
  width: auto;
}

#cookies-bar .title {
  margin-bottom: 0.25rem;
  font-weight: 700;
}

#cookies-bar .content p {
  margin-bottom: 0.75rem;
  display: inline;
}

#cookies-bar .content p:last-of-type {
  margin-bottom: 0px;
}

#cookies-bar .content a {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

#cookies-bar .content a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

#cookies-bar .buttons {
  position: relative;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  justify-content: space-between;
  row-gap: 1rem;
}

@media (min-width: 768px) {
  #cookies-bar .buttons {
    width: auto;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: normal;
    row-gap: 0.5rem;
  }
}

#cookies-bar .decline-btn {
  position: absolute;
  top: 0px;
  right: 1rem;
  text-align: left;
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  #cookies-bar .decline-btn {
    right: auto;
    left: 100%;
    --tw-translate-x: 1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

.cookie-btn-primary {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.cookie-btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

@media (min-width: 768px) {
  .cookie-btn-primary {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }
}

.cookie-btn-default {
  display: inline-block;
  cursor: pointer;
  white-space: nowrap;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.cookie-btn-default:hover {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .cookie-btn-default {
    width: 100%;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }
}

.cookie-btn-primary.hasDisableButton,
.cookie-btn-default.hasDisableButton {
  width: 83.333333%;
}

@media (min-width: 1024px) {
  .cookie-btn-primary.hasDisableButton,
.cookie-btn-default.hasDisableButton {
    width: 16rem;
  }
}

.cookie-btn-default svg {
  margin-left: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .cookie-btn-default svg {
    margin-left: 1rem;
    --tw-text-opacity: 1;
    color: rgb(163 163 163 / var(--tw-text-opacity, 1));
  }
}

#cookies-bar .detailsbutton {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

#cookies-bar .detailsbutton:hover {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

#cookies-bar .detailsbutton_mobile {
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

#cookies-bar .detailsbutton_mobile:hover {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.product-slider-dots .slick-dots button {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.autocomplete {
  position: relative;
  display: inline-block;
}

/* input {
    border: 1px solid transparent;
    background-color: #f1f1f1;
    padding: 10px;
    font-size: 16px;
  }
  input[type=text] {
    background-color: #f1f1f1;
    width: 100%;
  }
  input[type=submit] {
    background-color: DodgerBlue;
    color: #fff;
  } */

.autocomplete-items {
  position: absolute;
  border: 1px solid #d4d4d4;
  border-bottom: none;
  z-index: 99;
  top: 100%;
  left: .5rem;
  right: 0;
  border-radius: 8px;
  overflow: hidden;
}

.autocomplete-items div {
  padding: 8px 16px;
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid #d4d4d4;
}

.autocomplete-items div:hover {
  background-color: #e9e9e9;
}

.autocomplete-active {
  background-color: DodgerBlue !important;
  color: #ffffff;
}

input[data-name="housenumber"]::-webkit-list-button {
  display: none;
  opacity: 0;
}

#city_display_field {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#city_display_label {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@media (min-width: 768px) {
  #city_display_label {
    display: none;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  #city_display_label {
    margin-bottom: 0px;
  }
}

#city_display_label:not(:empty) {
  margin-bottom: 0.75rem;
}

#addressSelectorWrap input::-webkit-outer-spin-button,
#addressSelectorWrap input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */

#addressSelectorWrap input[type=number] {
  -moz-appearance: textfield;
}

.hero-sticker {
  right: 0px;
  top: 0px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 2rem;
  display: flex;
  width: 100%;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .hero-sticker {
    width: auto;
  }
}

@media (min-width: 1280px) {
  .hero-sticker {
    position: absolute;
    margin-left: 0px;
    margin-right: 0px;
    margin-top: 0px;
    height: 8rem;
    width: 8rem;
    border-radius: 9999px;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
  }

  .hero-sticker strong {
    display: block;
  }
}

.faq-answer a {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
  text-decoration-line: underline;
}

.faq-answer a:hover {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.form-hor-radiolist .form-hor-radio.invalid {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.form-hor-radiolist .radio label.invalid {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* OTP */

/* .wrapper-phone-otp { @apply mb-4; } */

.wrapper-phone-otp.less-mb {
  margin-bottom: 1rem;
}

.otp-heading {
  margin-bottom: 0.25rem;
  display: block;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.otp-validation p,
.otp-comment,
.otp-checkbox-text {
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.otp-comment {
  margin-bottom: 1rem;
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .otp-comment {
    margin-bottom: 0px;
  }
}

/* .otp-grid { @apply mt-4 md:flex md:flex-1 md:space-x-2 px-1; } */

.otp-grid {
  margin-top: 1rem;
}

/* .otp-field-grid { @apply md:col-span-8 grid grid-cols-6 gap-2 md:grid-cols-6 md:gap-1; } */

.otp-code-field {
  border-width: 2px;
  border-color: transparent;
}

.otp-code-field:focus {
  --tw-border-opacity: 1;
  border-color: rgb(23 23 23 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.otp-code-field {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.otp-button-wrap {
  grid-column: span 6 / span 6;
  margin-top: 0.75rem;
}

@media (min-width: 768px) {
  .otp-button-wrap {
    grid-column: span 4 / span 4;
    margin-top: 0px;
  }
}

.otp-send-sms,
.otp-validation {
  margin-top: 1rem;
  padding-bottom: 0.25rem;
}

.otp-send-sms-button,
.verify-otp-code {
  display: inline-flex;
  align-items: center;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.otp-send-sms-button:hover,
.verify-otp-code:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.otp-send-sms-checkbox {
  text-align: left;
}

.otp-send-sms-checkbox .otp-checkbox-empty {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.otp-send-sms-checkbox .otp-checkbox-checked {
  --tw-text-opacity: 1;
  color: rgb(154 196 77 / var(--tw-text-opacity, 1));
}

.otp-checkbox-text {
  flex: 1 1 auto;
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.otp-send-sms-checkbox .otp-checkbox {
  display: inline-flex;
  height: 1.75rem;
  width: 1.75rem;
  flex-shrink: 0;
  flex-grow: 1;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.otp-send-sms-checkbox .otp-checkbox.invalid {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.otp-send-sms-checkbox .otp-checkbox.otp-checked {
  --tw-border-opacity: 1;
  border-color: rgb(194 220 148 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(235 243 219 / var(--tw-bg-opacity, 1));
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(154 196 77 / var(--tw-text-opacity, 1));
}

.wrapper-phone-otp.invalid .otp-send-sms-checkbox .otp-checkbox {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.wrapper-phone-otp.invalid .otp-send-sms-checkbox .otp-checkbox-checked {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.wrapper-phone-otp.invalid .otp-send-sms-checkbox .otp-checkbox-text {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.wrapper-phone-otp .otp-send-sms.otp-sms-sent .otp-send-sms-checkbox .otp-checkbox-text {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .otp-tel-wrap {
    padding-left: 0.5rem;
  }
}

.fhg-top {
  align-items: flex-start;
  row-gap: 0px;
}

.fhg-top .form-hor-label {
  margin-top: 0.5rem;
}

.otp-result-failed,
.otp-result-success {
  margin-top: 1rem;
  display: none;
  border-radius: 0.375rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.otp-result-failed {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.otp-result-success {
  --tw-bg-opacity: 1;
  background-color: rgb(215 231 184 / var(--tw-bg-opacity, 1));
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(92 118 46 / var(--tw-text-opacity, 1));
}

.otp-change-phone-prompt {
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  display: none;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 15px;
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  .otp-change-phone-prompt {
    margin-bottom: 0px;
  }
}

.otp-change-phone-prompt button:hover {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.otp-disabled-input {
  position: absolute;
  inset: 0px;
  z-index: 10;
}

.btn-submit-overlay {
  position: absolute;
  inset: 0px;
  display: none;
}

.peer:disabled ~ .btn-submit-overlay {
  display: block;
}

.wrapper-phone-otp.invalid .otp-code-field {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

a.deploy_all_sites {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

a.deploy_all_sites:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

a.deploy_all_sites.deployment_started {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity, 1));
}

a.deploy_all_sites.deployment_started:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity, 1));
}

.checkboxlist-required .checkbox.not-valid > label {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.lb-close {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.lb-nav a.lb-prev,
.lb-nav a.lb-next {
  opacity: .8 !important;
}

.lb-nav a.lb-prev:hover,
.lb-nav a.lb-next:hover {
  opacity: 1 !important;
}

.lb-dataContainer {
  position: absolute;
  top: 0px;
  left: 50%;
  z-index: 20;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.lb-dataContainer .lb-data {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
}

@media (min-width: 768px) {
  .form-hor-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.form-hor-newsletter {
  display: flex;
  align-items: center;
}

.form-hor-newsletter > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.form-hor-newsletter .form-hor-radio {
  margin-right: 0.5rem;
}

#newsletter_yes {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.form-hor-newsletter .checkbox-box {
  display: flex;
  height: 1.75rem;
  width: 1.75rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.form-hor-newsletter .checkbox-box.invalid {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.form-hor-newsletter .checkbox-box .checkmark {
  display: none;
}

#newsletter_yes:checked ~ .checkbox-box {
  --tw-border-opacity: 1;
  border-color: rgb(194 220 148 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(235 243 219 / var(--tw-bg-opacity, 1));
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(154 196 77 / var(--tw-text-opacity, 1));
}

#newsletter_yes:checked ~ .checkbox-box .checkmark {
  display: block;
}

.newsletter-field-wrap [data-name="zipcode"]::-moz-placeholder {
  text-transform: none;
}

.newsletter-field-wrap [data-name="zipcode"]::placeholder {
  text-transform: none;
}

.no-mb {
  margin-bottom: 0px;
}

.newsletter-wrapper {
  margin-top: 0.75rem;
}

.mail-newsletter-label {
  font-size: 13px;
}

@media (min-width: 640px) {
  .mail-newsletter-label {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {
  .mail-newsletter-label {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.email-autocomplete {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  opacity: 0.5;
}

.cookies-decline-all {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 768px) {
  .cookies-decline-all {
    text-align: right;
  }
}

.upload-form-group .form-hor-grid-3,
.upload-form-group.wide .form-hor-grid-3 {
  align-items: flex-start;
}

.upload-form-group ul.upload-file-list {
  margin-top: 0.5rem;
}

.upload-form-group ul.upload-file-list > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-divide-opacity, 1));
}

.upload-form-group ul.upload-file-list {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.upload-form-group ul.upload-file-list:empty {
  display: none;
}

.upload-form-group ul.upload-file-list .uploaded-file {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.upload-form-group input[data-name="upload-field"] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.upload-form-group label.upload-button {
  display: inline-flex;
  align-items: center;
}

.upload-form-group label.upload-button > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.upload-form-group label.upload-button {
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.upload-form-group label.upload-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-600), var(--tw-bg-opacity, 1));
}

.upload-form-group button.remove-uploads {
  display: inline-flex;
  align-items: center;
}

.upload-form-group button.remove-uploads > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.upload-form-group button.remove-uploads {
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.upload-form-group button.remove-uploads:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.upload-form-group .comment {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.upload-form-group p.upload-alert {
  margin-top: 0.25rem;
  display: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-style: italic;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.upload-form-group.invalid p.upload-alert {
  display: block;
}

#form-2025 .form-btn {
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(14 111 12 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(10 183 4 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

#form-2025 .form-btn:hover {
  --tw-border-opacity: 1;
  border-color: rgb(10 142 7 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(20 201 14 / var(--tw-bg-opacity, 1));
}

#form-2025 .form-btn:disabled {
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}

#form-2025 .custom_heading h1,
#form-2025 .custom_heading h2,
#form-2025 .custom_heading h3,
#form-2025 .custom_heading h4 {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

@media (min-width: 768px) {
  #form-2025 .custom_heading h1,
#form-2025 .custom_heading h2,
#form-2025 .custom_heading h3,
#form-2025 .custom_heading h4 {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}

#form-2025 .formHasTitleAbove .steps-wrap {
  margin: 0px;
  margin-bottom: 2rem;
  background-color: transparent;
  padding: 0px;
}

@media (min-width: 1024px) {
  #form-2025 .formHasTitleAbove .steps-wrap {
    margin-bottom: 3rem;
  }
}

#form-2025 .formHasTitleAbove .steps .step {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

@media (min-width: 1024px) {
  #form-2025 .formHasTitleAbove .steps .step {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

footer .eac-input-wrap {
  width: 100% !important;
}

#thankyoupage_otp .form-hor-field {
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

#thankyoupage_otp .otp-validation {
  margin-top: 0.5rem;
}

#thankyoupage_otp .otp-validation p {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary-100), var(--tw-text-opacity, 1));
}

#thankyoupage_otp .form-hor-wrap {
  margin-bottom: 0px;
}

@media (min-width: 768px) {
  #thankyoupage_otp .form-hor-wrap {
    margin-bottom: 0px;
  }
}

@media (min-width: 1024px) {
  #thankyoupage_otp .form-hor-wrap {
    margin-bottom: 0px;
  }
}

#thankyoupage_otp .form-hor-label {
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.addressfinder_field .autocomplete-container {
  position: relative;
}

/* Suggestions dropdown */

.addressfinder_field .suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 6px 6px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  list-style: none;
  margin: 0;
  padding: 0;
  display: none;
}

.addressfinder_field .suggestions-dropdown.show {
  display: block;
}

.addressfinder_field .suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.addressfinder_field .suggestion-item:hover,
.addressfinder_field .suggestion-item.highlighted {
  background-color: #f8f9fa;
}

.addressfinder_field .suggestion-item:last-child {
  border-bottom: none;
}

.addressfinder_field .suggestion-main {
  font-weight: 600;
  color: #333;
}

.addressfinder_field .suggestion-secondary {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.addressfinder_field button.address_unknown {
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(var(--color-primary-400), var(--tw-ring-opacity, 1));
}

.addressfinder_field button.address_unknown:hover {
  text-decoration-line: underline;
}

.addressfinder_field button.address_unknown:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 1px;
}

.addressfinder_field .streetview-block {
  margin-top: 1rem;
}

.addressfinder_field .streetview-block .streetview[src*="maps.googleapis.com"] {
  border-radius: 0.375rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 640px) {
  .sm\:prose-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .sm\:prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .sm\:prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .sm\:prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-inline-start: 1em;
  }

  .sm\:prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .sm\:prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.6666667em;
    margin-top: 1.8666667em;
    margin-bottom: 1.0666667em;
    line-height: 1.3333333;
  }

  .sm\:prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.5;
  }

  .sm\:prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .sm\:prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .sm\:prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .sm\:prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .sm\:prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .sm\:prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-inline-end: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-inline-start: 0.4444444em;
  }

  .sm\:prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .sm\:prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .sm\:prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .sm\:prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-inline-end: 1.5em;
    padding-bottom: 1em;
    padding-inline-start: 1.5em;
  }

  .sm\:prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .sm\:prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .sm\:prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .sm\:prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .sm\:prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .sm\:prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .sm\:prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .sm\:prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .sm\:prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-inline-start: 1.5555556em;
  }

  .sm\:prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .sm\:prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .sm\:prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .sm\:prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .sm\:prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .sm\:prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .sm\:prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .sm\:prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .sm\:prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .sm\:prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .sm\:prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .sm\:prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .sm\:prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .sm\:prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .sm\:prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .sm\:prose-lg :where(.sm\:prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .lg\:container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
  }

  @media (min-width: 576px) {
    .lg\:container {
      max-width: 576px;
    }
  }

  @media (min-width: 768px) {
    .lg\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 992px) {
    .lg\:container {
      max-width: 992px;
    }
  }

  @media (min-width: 1170px) {
    .lg\:container {
      max-width: 1170px;
    }
  }

  .lg\:prose-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .lg\:prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .lg\:prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-inline-start: 1em;
  }

  .lg\:prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .lg\:prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.6666667em;
    margin-top: 1.8666667em;
    margin-bottom: 1.0666667em;
    line-height: 1.3333333;
  }

  .lg\:prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .lg\:prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-inline-end: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .lg\:prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .lg\:prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .lg\:prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-inline-end: 1.5em;
    padding-bottom: 1em;
    padding-inline-start: 1.5em;
  }

  .lg\:prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .lg\:prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .lg\:prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }

  .lg\:prose-xl {
    font-size: 1.25rem;
    line-height: 1.8;
  }

  .lg\:prose-xl :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
  }

  .lg\:prose-xl :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2em;
    line-height: 1.5;
    margin-top: 1em;
    margin-bottom: 1em;
  }

  .lg\:prose-xl :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-inline-start: 1.0666667em;
  }

  .lg\:prose-xl :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.8em;
    margin-top: 0;
    margin-bottom: 0.8571429em;
    line-height: 1;
  }

  .lg\:prose-xl :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.8em;
    margin-top: 1.5555556em;
    margin-bottom: 0.8888889em;
    line-height: 1.1111111;
  }

  .lg\:prose-xl :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.5em;
    margin-top: 1.6em;
    margin-bottom: 0.6666667em;
    line-height: 1.3333333;
  }

  .lg\:prose-xl :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.8em;
    margin-bottom: 0.6em;
    line-height: 1.6;
  }

  .lg\:prose-xl :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .lg\:prose-xl :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .lg\:prose-xl :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-xl :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .lg\:prose-xl :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
    border-radius: 0.3125rem;
    padding-top: 0.25em;
    padding-inline-end: 0.4em;
    padding-bottom: 0.25em;
    padding-inline-start: 0.4em;
  }

  .lg\:prose-xl :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
  }

  .lg\:prose-xl :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8611111em;
  }

  .lg\:prose-xl :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
  }

  .lg\:prose-xl :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
    line-height: 1.7777778;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.5rem;
    padding-top: 1.1111111em;
    padding-inline-end: 1.3333333em;
    padding-bottom: 1.1111111em;
    padding-inline-start: 1.3333333em;
  }

  .lg\:prose-xl :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    padding-inline-start: 1.6em;
  }

  .lg\:prose-xl :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    padding-inline-start: 1.6em;
  }

  .lg\:prose-xl :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6em;
    margin-bottom: 0.6em;
  }

  .lg\:prose-xl :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4em;
  }

  .lg\:prose-xl :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8em;
    margin-bottom: 0.8em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.2em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.2em;
  }

  .lg\:prose-xl :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8em;
    margin-bottom: 0.8em;
  }

  .lg\:prose-xl :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
    margin-bottom: 1.2em;
  }

  .lg\:prose-xl :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.2em;
  }

  .lg\:prose-xl :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6em;
    padding-inline-start: 1.6em;
  }

  .lg\:prose-xl :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 2.8em;
    margin-bottom: 2.8em;
  }

  .lg\:prose-xl :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-xl :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-xl :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-xl :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-xl :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
    line-height: 1.5555556;
  }

  .lg\:prose-xl :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.6666667em;
    padding-bottom: 0.8888889em;
    padding-inline-start: 0.6666667em;
  }

  .lg\:prose-xl :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-xl :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-xl :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.8888889em;
    padding-inline-end: 0.6666667em;
    padding-bottom: 0.8888889em;
    padding-inline-start: 0.6666667em;
  }

  .lg\:prose-xl :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-xl :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-xl :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .lg\:prose-xl :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-xl :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.9em;
    line-height: 1.5555556;
    margin-top: 1em;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-xl :where(.lg\:prose-xl > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}

.selection\:bg-primary-500 *::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.selection\:bg-primary-500 *::selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.selection\:bg-secondary-500 *::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.selection\:bg-secondary-500 *::selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.selection\:text-white *::-moz-selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:text-white *::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:bg-primary-500::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.selection\:bg-primary-500::selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
}

.selection\:bg-secondary-500::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.selection\:bg-secondary-500::selection {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary), var(--tw-bg-opacity, 1));
}

.selection\:text-white::-moz-selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:text-white::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.after\:hidden::after {
  content: var(--tw-content);
  display: none;
}

.last-of-type\:border-b-0:last-of-type {
  border-bottom-width: 0px;
}

.valid\:border-lime-500:valid {
  --tw-border-opacity: 1;
  border-color: rgb(132 204 22 / var(--tw-border-opacity, 1));
}

.invalid\:border-red-500:invalid {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.hover\:border-apple-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(10 142 7 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#c1f1e9\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(193 241 233 / var(--tw-bg-opacity, 1));
}

.hover\:bg-apple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(20 201 14 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-300\/80:hover {
  background-color: rgb(165 180 252 / 0.8);
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-200\/70:hover {
  background-color: rgb(254 215 170 / 0.7);
}

.hover\:bg-orange-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-200), var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-600), var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-primary-700), var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200\/70:hover {
  background-color: rgb(254 202 202 / 0.7);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary-200), var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-secondary-600), var(--tw-bg-opacity, 1));
}

.hover\:bg-tertiary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(var(--color-tertiary-600), var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.hover\:text-inherit:hover {
  color: inherit;
}

.hover\:text-lime-900:hover {
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-800:hover {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-800:hover {
  --tw-text-opacity: 1;
  color: rgb(7 89 133 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-900:hover {
  --tw-text-opacity: 1;
  color: rgb(12 74 110 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-700:hover {
  --tw-text-opacity: 1;
  color: rgb(15 118 110 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.hover\:\!opacity-100:hover {
  opacity: 1 !important;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-black\/10:hover {
  --tw-shadow-color: rgb(0 0 0 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.focus\:border-gray-700:focus {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(115 115 115 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(var(--color-primary-400), var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(var(--color-primary), var(--tw-ring-opacity, 1));
}

.focus\:ring-secondary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(var(--color-secondary), var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-50:focus {
  --tw-ring-offset-color: #fafafa;
}

.focus\:valid\:border-lime-600:valid:focus {
  --tw-border-opacity: 1;
  border-color: rgb(101 163 13 / var(--tw-border-opacity, 1));
}

.group:hover .group-hover\:\!visible {
  visibility: visible !important;
}

.group:hover .group-hover\:\!block {
  display: block !important;
}

.group:hover .group-hover\:bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-secondary-600 {
  --tw-text-opacity: 1;
  color: rgba(var(--color-secondary-600), var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.prose-headings\:mb-4 :is(:where(h1, h2, h3, h4, h5, h6, th):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-bottom: 1rem;
}

.prose-h2\:mb-4 :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-bottom: 1rem;
}

.prose-h3\:font-bold :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-weight: 700;
}

.prose-p\:text-gray-800 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.prose-a\:text-primary-600 :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary-600), var(--tw-text-opacity, 1));
}

.prose-a\:no-underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  text-decoration-line: none;
}

.hover\:prose-a\:text-primary-500 :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))):hover {
  --tw-text-opacity: 1;
  color: rgba(var(--color-primary), var(--tw-text-opacity, 1));
}

.hover\:prose-a\:underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))):hover {
  text-decoration-line: underline;
}

.prose-table\:my-0 :is(:where(table):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-top: 0px;
  margin-bottom: 0px;
}

.prose-hr\:mb-4 :is(:where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-bottom: 1rem;
}

.prose-hr\:w-14 :is(:where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  width: 3.5rem;
}

.prose-hr\:border-t-4 :is(:where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  border-top-width: 4px;
}

.prose-hr\:border-secondary-500 :is(:where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-border-opacity: 1;
  border-color: rgba(var(--color-secondary), var(--tw-border-opacity, 1));
}

@media (min-width: 640px) {
  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-16 {
    margin-left: 4rem;
    margin-right: 4rem;
  }

  .sm\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:ml-0 {
    margin-left: 0px;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:mr-4 {
    margin-right: 1rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:h-screen {
    height: 100vh;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-8 {
    width: 2rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:max-w-4xl {
    max-width: 56rem;
  }

  .sm\:max-w-5xl {
    max-width: 64rem;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:border-r {
    border-right-width: 1px;
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
  }

  .sm\:pl-6 {
    padding-left: 1.5rem;
  }

  .sm\:pr-0 {
    padding-right: 0px;
  }

  .sm\:pr-4 {
    padding-right: 1rem;
  }

  .sm\:pt-16 {
    padding-top: 4rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:align-middle {
    vertical-align: middle;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:prose-h2\:mb-4 :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    margin-bottom: 1rem;
  }
}

@media (min-width: 768px) {
  .md\:absolute {
    position: absolute;
  }

  .md\:inset-x-0 {
    left: 0px;
    right: 0px;
  }

  .md\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .md\:-left-5 {
    left: -1.25rem;
  }

  .md\:-left-8 {
    left: -2rem;
  }

  .md\:-right-5 {
    right: -1.25rem;
  }

  .md\:-top-8 {
    top: -2rem;
  }

  .md\:bottom-10 {
    bottom: 2.5rem;
  }

  .md\:left-0 {
    left: 0px;
  }

  .md\:left-auto {
    left: auto;
  }

  .md\:right-0 {
    right: 0px;
  }

  .md\:right-full {
    right: 100%;
  }

  .md\:top-1\/2 {
    top: 50%;
  }

  .md\:z-20 {
    z-index: 20;
  }

  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:-m-10 {
    margin: -2.5rem;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-24 {
    margin-left: 6rem;
    margin-right: 6rem;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .md\:my-16 {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }

  .md\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .md\:-mr-16 {
    margin-right: -4rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:ml-0 {
    margin-left: 0px;
  }

  .md\:ml-6 {
    margin-left: 1.5rem;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:mt-16 {
    margin-top: 4rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline {
    display: inline;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-flex {
    display: inline-flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-2\/3 {
    height: 66.666667%;
  }

  .md\:h-32 {
    height: 8rem;
  }

  .md\:h-4 {
    height: 1rem;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-\[117px\] {
    height: 117px;
  }

  .md\:h-\[50px\] {
    height: 50px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:min-h-full {
    min-height: 100%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-1\/4 {
    width: 25%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-2\/4 {
    width: 50%;
  }

  .md\:w-32 {
    width: 8rem;
  }

  .md\:w-4 {
    width: 1rem;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:w-\[300px\] {
    width: 300px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:grow {
    flex-grow: 1;
  }

  .md\:-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-1\/2 {
    --tw-translate-x: 50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-full {
    --tw-translate-x: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:rotate-0 {
    --tw-rotate: 0deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-14 {
    gap: 3.5rem;
  }

  .md\:gap-16 {
    gap: 4rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-24 {
    gap: 6rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .md\:divide-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }

  .md\:whitespace-nowrap {
    white-space: nowrap;
  }

  .md\:rounded-lg {
    border-radius: 0.5rem;
  }

  .md\:rounded-r-lg {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
  }

  .md\:bg-primary-500 {
    --tw-bg-opacity: 1;
    background-color: rgba(var(--color-primary), var(--tw-bg-opacity, 1));
  }

  .md\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:pb-20 {
    padding-bottom: 5rem;
  }

  .md\:pb-4 {
    padding-bottom: 1rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-4 {
    padding-left: 1rem;
  }

  .md\:pl-8 {
    padding-left: 2rem;
  }

  .md\:pr-16 {
    padding-right: 4rem;
  }

  .md\:pr-3 {
    padding-right: 0.75rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:pt-2\.5 {
    padding-top: 0.625rem;
  }

  .md\:pt-4 {
    padding-top: 1rem;
  }

  .md\:pt-8 {
    padding-top: 2rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-center {
    text-align: center;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-lgr {
    font-size: 1.375rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:font-semibold {
    font-weight: 600;
  }

  .md\:leading-normal {
    line-height: 1.5;
  }

  .md\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .md\:opacity-100 {
    opacity: 1;
  }

  .md\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .md\:shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1024px) {
  .lg\:absolute {
    position: absolute;
  }

  .lg\:relative {
    position: relative;
  }

  .lg\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .lg\:-left-16 {
    left: -4rem;
  }

  .lg\:-top-12 {
    top: -3rem;
  }

  .lg\:-top-14 {
    top: -3.5rem;
  }

  .lg\:bottom-0 {
    bottom: 0px;
  }

  .lg\:left-0 {
    left: 0px;
  }

  .lg\:left-full {
    left: 100%;
  }

  .lg\:right-0 {
    right: 0px;
  }

  .lg\:right-11 {
    right: 2.75rem;
  }

  .lg\:right-14 {
    right: 3.5rem;
  }

  .lg\:right-auto {
    right: auto;
  }

  .lg\:top-1\/2 {
    top: 50%;
  }

  .lg\:top-12 {
    top: 3rem;
  }

  .lg\:top-14 {
    top: 3.5rem;
  }

  .lg\:z-10 {
    z-index: 10;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-start-1 {
    grid-column-start: 1;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .lg\:row-end-1 {
    grid-row-end: 1;
  }

  .lg\:row-end-2 {
    grid-row-end: 2;
  }

  .lg\:m-0 {
    margin: 0px;
  }

  .lg\:-my-8 {
    margin-top: -2rem;
    margin-bottom: -2rem;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mx-32 {
    margin-left: 8rem;
    margin-right: 8rem;
  }

  .lg\:mx-8 {
    margin-left: 2rem;
    margin-right: 2rem;
  }

  .lg\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-16 {
    margin-bottom: 4rem;
  }

  .lg\:mb-32 {
    margin-bottom: 8rem;
  }

  .lg\:ml-0 {
    margin-left: 0px;
  }

  .lg\:mr-4 {
    margin-right: 1rem;
  }

  .lg\:mr-8 {
    margin-right: 2rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-4 {
    margin-top: 1rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .lg\:h-1\/2 {
    height: 50%;
  }

  .lg\:h-32 {
    height: 8rem;
  }

  .lg\:h-36 {
    height: 9rem;
  }

  .lg\:h-48 {
    height: 12rem;
  }

  .lg\:h-\[72px\] {
    height: 72px;
  }

  .lg\:h-full {
    height: 100%;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-32 {
    width: 8rem;
  }

  .lg\:w-36 {
    width: 9rem;
  }

  .lg\:w-4\/6 {
    width: 66.666667%;
  }

  .lg\:w-48 {
    width: 12rem;
  }

  .lg\:w-\[22\%\] {
    width: 22%;
  }

  .lg\:w-\[33\%\] {
    width: 33%;
  }

  .lg\:w-\[45\%\] {
    width: 45%;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:max-w-7xl {
    max-width: 80rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:flex-auto {
    flex: 1 1 auto;
  }

  .lg\:-translate-x-14 {
    --tw-translate-x: -3.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:-translate-x-4 {
    --tw-translate-x: -1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:-translate-y-6 {
    --tw-translate-y: -1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-x-24 {
    --tw-translate-x: 6rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-x-6 {
    --tw-translate-x: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:rotate-0 {
    --tw-rotate: 0deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-flow-col-dense {
    grid-auto-flow: column dense;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-24 {
    gap: 6rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-5 {
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
  }

  .lg\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .lg\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .lg\:gap-y-3 {
    row-gap: 0.75rem;
  }

  .lg\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }

  .lg\:divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  .lg\:divide-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }

  .lg\:divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(212 212 212 / var(--tw-divide-opacity, 1));
  }

  .lg\:overflow-hidden {
    overflow: hidden;
  }

  .lg\:rounded-md {
    border-radius: 0.375rem;
  }

  .lg\:rounded-xl {
    border-radius: 0.75rem;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-18 {
    padding-top: 4.5rem;
    padding-bottom: 4.5rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .lg\:py-7 {
    padding-top: 1.75rem;
    padding-bottom: 1.75rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-16 {
    padding-bottom: 4rem;
  }

  .lg\:pb-24 {
    padding-bottom: 6rem;
  }

  .lg\:pb-32 {
    padding-bottom: 8rem;
  }

  .lg\:pl-14 {
    padding-left: 3.5rem;
  }

  .lg\:pl-16 {
    padding-left: 4rem;
  }

  .lg\:pl-24 {
    padding-left: 6rem;
  }

  .lg\:pl-8 {
    padding-left: 2rem;
  }

  .lg\:pr-0 {
    padding-right: 0px;
  }

  .lg\:pr-14 {
    padding-right: 3.5rem;
  }

  .lg\:pr-16 {
    padding-right: 4rem;
  }

  .lg\:pr-24 {
    padding-right: 6rem;
  }

  .lg\:pr-8 {
    padding-right: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:pt-16 {
    padding-top: 4rem;
  }

  .lg\:pt-32 {
    padding-top: 8rem;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-lgr {
    font-size: 1.375rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:leading-tight {
    line-height: 1.25;
  }

  .lg\:shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .lg\:prose-h2\:mb-4 :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    margin-bottom: 1rem;
  }
}

@media (min-width: 1280px) {
  .xl\:-top-1\/2 {
    top: -50%;
  }

  .xl\:my-32 {
    margin-top: 8rem;
    margin-bottom: 8rem;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mr-4 {
    margin-right: 1rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:inline-block {
    display: inline-block;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:h-48 {
    height: 12rem;
  }

  .xl\:w-48 {
    width: 12rem;
  }

  .xl\:max-w-\[70\%\] {
    max-width: 70%;
  }

  .xl\:max-w-none {
    max-width: none;
  }

  .xl\:-translate-x-16 {
    --tw-translate-x: -4rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .xl\:justify-start {
    justify-content: flex-start;
  }

  .xl\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .xl\:p-12 {
    padding: 3rem;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .xl\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xl\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .xl\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .xl\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .xl\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .xl\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .xl\:text-lgr {
    font-size: 1.375rem;
  }
}

.dark\:bg-gray-100:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
