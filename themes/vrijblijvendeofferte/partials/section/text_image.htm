{% set responsive = fields.responsive %}
{% set show_mobile = responsive.show_mobile %}
{% set show_tablet = responsive.show_tablet %}
{% set show_laptop = responsive.show_laptop %}
{% set show_desktop = responsive.show_desktop %}

{% set bg = 'bg-white' %}
{% set imgBg = 'bg-gray-100' %}
{% if fields.background == 'gray' %}
  {% set bg = 'bg-gray-100' %}
  {% set imgBg = 'bg-gray-200' %}
{% endif %}
{% if fields.background == 'white' %}
  {% set bg = 'bg-white' %}
  {% set imgBg = 'bg-gray-100' %}
{% endif %}

<section class="{{ bg }} py-8 md:py-16 lg:pb-16 lg:pt-32
{{ show_mobile ? 'block' : 'hidden' }}
{{ show_tablet ? 'md:block' : 'md:hidden' }}
{{ show_laptop ? 'lg:block' : 'lg:hidden' }}
{{ show_desktop ? 'xl:block' : 'xl:hidden' }}
">
  <div class="container">
    <div class="flex flex-wrap md:grid md:grid-cols-2 gap-8">
      <div class="{{ fields.position == 'left' ? 'order-1 md:order-2 lg:pl-14' : 'order-1 md:order-1 lg:pr-14' }} {{ fields.shadow ? 'lg:pb-32' }}">

        <div class="prose prose-primary prose-lg lg:prose-xl prose-h2:mb-4 lg:prose-h2:mb-4 max-w-none">
          <h2>{{ fields.title }}</h2>
          {{ fields.content|raw }}
        </div>

        {% if fields.has_list %}
        <div class="mt-6">
          <h4 class="text-black text-xl font-bold mb-3">{{ fields.list_title }}</h4>

          <ul class="space-y-2">
            {% for item in fields.list %}
              {% partial "ui/list_item" list_item=item.list_item %}
            {% endfor %}
          </ul>

        </div>

        {% endif %}

        {% if fields.has_buttons %}
          <div class="mt-6">
              {% for item in fields.buttons %}
                {% partial "ui/button" item=item text=item.text icon=item.icon url=item.url size=item.size style=item.style offerlink=item.offerte_button external=item.external_link link_button=item.link_button %}
              {% endfor %}
          </div>
        {% endif %}
      </div>

      <div class="w-full md:pl-8 relative md:pt-8 lg:pt-0 {{ fields.position == 'left' ? 'order-2 md:order-1 lg:pr-16' : 'order-2 md:order-2 lg:pl-16' }}">
        {% if fields.ratio %}
            <div class="hidden md:flex md:items-center relative aspect-square {% if fields.shadow %}{{ imgBg }} {% endif %}">
                <img src="{{ fields.image|media|resize(786, null, { extension: 'webp', filename: true, quality: 80 }) }}" alt="" class="object-cover w-full h-full {{ fields.shadow ? 'md:-top-8 lg:-top-14 md:-left-8 lg:-left-16' }} {{ fields.ratio ? 'md:absolute' }} lazy">
            </div>
        {% else %}
            <div class="hidden md:block lg:absolute inset-0 left-16 {% if fields.shadow %}{{ imgBg }} {% endif %}">
            <img src="{{ fields.image|media|resize(786, null, { extension: 'webp', filename: true, quality: 80 }) }}" alt="" class="object-cover w-full h-full {{ fields.shadow ? 'relative -top-8 lg:-top-14 -left-8 lg:-left-16' }} lazy">
            </div>
        {% endif %}
        <div class="md:hidden">
            <img src="{{ fields.image|media|resize(600, null, { extension: 'webp', filename: true, quality: 80 }) }}" alt="" class="lazy">
        </div>
      </div>
    </div>
  </div>
</section>
