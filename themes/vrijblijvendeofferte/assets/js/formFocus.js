jQuery(function() {
    var form = $('#form_default form');

    $(form).on('click', '.form-btn-next, .form-btn-prev', function(){
        var active_step = $(this).closest('form').data('active_step');

        if ( active_step != 'form-step-1' ) {
            $('#hero-2025-image').addClass('hidden lg:flex');
            $('#hero-2025-sidebar').addClass('hidden lg:block');
            $('#hero-2025-content').hide();
            $('#hero-2025-reviews').hide();
            $('.oc-box:not(".oc-box--first")').hide();

            $('#hero-2025-formcontainer')
                .removeClass('md:pt-16')
                .addClass('lg:pt-0');

            $('#navbar_topbar').slideUp();
            $('#hero-2025-formcontainer #form_wrapper').removeClass('mb-10');
        }
    });

    $(form).on('click', '.form-btn-prev', function(){
        var active_step = $(this).closest('form').data('active_step');

        if ( active_step == 'form-step-1' ) {
            $('#hero-2025-image').removeClass('hidden lg:flex');
            $('#hero-2025-content').show();
            $('#hero-2025-reviews').show();
            $('.oc-box:not(".oc-box--first")').show();

            $('#hero-2025-formcontainer')
                .addClass('pt-12')
                .addClass('md:pt-16');

            $('#hero-2025-formcontainer #form_wrapper').addClass('mb-10');
        }
    });
});
