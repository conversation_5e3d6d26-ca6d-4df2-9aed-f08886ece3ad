<div class="absolute inset-x-0 bottom-0 py-1.5 bg-gray-100">
    <div
        class="flex {{ not isFocusLayout ? 'px-5' }} md:px-12 {{ box.veiligeVerbinding ? 'justify-between' : 'justify-center' }} md:items-center"
        {% if isFocusLayout %}
            :class="{ 'px-5': !formContainerTouchesTop, 'px-12 border-b border-b-300 pb-2 md:px-5 md:border-none md:pb-0': formContainerTouchesTop }"
        {% endif %}
    >
        {% if box.veiligeVerbinding %}
            <div class="flex space-x-2 items-center justify-center md:justify-start">
                <div class="w-4 h-4 md:w-6 md:h-6 rounded-full bg-gray-300 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-2 h-2 md:w-4 md:h-4 text-white">
                        <path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <span class="text-gray-800 text-center text-xs md:text-lg">
                    {{ box.veiligeVerbindingTekst | default('Veilige verbinding')|replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie}) }}
                </span>
            </div>
        {% endif %}

        {% if box.textUnderForm %}
            <p class="text-gray-800 text-center text-xs md:text-lg ">{{ box.textUnderForm|replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie}) }}</p>
        {% endif %}
    </div>
</div>
