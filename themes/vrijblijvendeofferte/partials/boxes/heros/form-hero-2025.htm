{% set isFocusLayout = this.page.layout == 'boxes-form-clean' %}

<style>
    .navbar_custom_html .fi { border-radius: 4px; }
</style>

<div class="relative bg-[#F5F5F5] md:bg-gray-100 wrapper" id="form-2025"{% if isFocusLayout %} x-data="{ formInViewport: false, contentViewport: 100 }"{% endif %}>
    {% if box.header_background_image %}
        {% set headerImageXl = box.header_background_image|media|resize(1920, 625, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
        {% set headerImageLg = box.header_background_image|media|resize(1600, 625, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
        {% set headerImageMd = box.header_background_image|media|resize(1400, 625, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
        {% set headerImageSm = box.header_background_image|media|resize(786, 550, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
        {% set headerImageXs = box.header_background_image|media|resize(786, 300, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
    {% endif %}
    {% if box.mobile_image %}
        {% set mobileImage = box.mobile_image|media|resize(786, 300, { mode: 'crop', extension: 'webp', filename: true, quality: 90 }) %}
    {% endif %}

    <div class="p-4 md:absolute md:inset-x-0 flex justify-center" id="hero-2025-image">
        <div class="rounded-xl overflow-hidden relative {{ box.no_mobile_image ? 'hidden md:block' }}">
            {% if box.mobile_image %}
                <img src="{{ mobileImage }}" alt="" class="md:hidden">
            {% endif %}
            <picture class="relative {{ box.mobile_image ? 'hidden md:block' }}">
                <source media="(max-width: 767px)" srcset="{{ headerImageXs }}" />
                <source media="(max-width: 1023px)" srcset="{{ headerImageSm }}" />
                <source media="(max-width: 1279px)" srcset="{{ headerImageMd }}" />
                <source media="(max-width: 1534px)" srcset="{{ headerImageLg }}" />
                <source media="(min-width: 1536px)" srcset="{{ headerImageXl }}" />
                <img src="{{ headerImageXl }}" alt="" />
            </picture>

            <div class="hidden md:block absolute w-[1200px] h-[1200px] top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 rounded-full aspect-square blur-3xl" style="background-color: {{ box.header_bg_image_overlay }}; opacity: {{ box.header_bg_image_overlay_alpha }}%;"></div>

        </div>
    </div>

    <div class="relative md:pt-4">
        <div class="{{ not box.mobileTags ? 'pb-6 md:pb-0' }} md:pt-8 lg:py-16 {{ box.title_above_form ? 'lg:pb-24' }}">
            <div
                class="container relative md:px-8 xl:px-4 transition-all ease-in duration-200"
                id="hero-2025-content"
                {% if isFocusLayout %}
                    x-intersect:enter.threshold.75="contentViewport = 75"
                    x-intersect:enter.threshold.50="contentViewport = 50"
                    x-intersect:enter.threshold.25="contentViewport = 25"
                    x-intersect:enter.full="contentViewport = 100"
                    x-intersect:leave.threshold.75="contentViewport = 75"
                    x-intersect:leave.threshold.50="contentViewport = 50"
                    x-intersect:leave.threshold.25="contentViewport = 25"
                    x-intersect:leave="contentViewport = 0"
                    :class="{
                        'opacity-100 md:opacity-100': contentViewport > 75,
                        'opacity-60 md:opacity-100': contentViewport === 75,
                        'opacity-40 md:opacity-100': contentViewport === 50,
                        'opacity-20 md:opacity-100': contentViewport === 25,
                        'opacity-0 md:opacity-100': contentViewport === 0
                    }"
                {% endif %}>

                <div class="mb-0 md:mb-8">
                    <h1 class="text-2xl md:text-4xl lg:text-5xl md:leading-normal lg:leading-tight text-body md:text-white font-bold tracking-tight">
                        {{ box.title|replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie})|raw }}
                    </h1>
                    <div class="font-medium md:text-xl lg:text-2xl text-body md:text-white md:font-semibold mt-2 md:mt-6">
                        {{ box.text | content }}
                    </div>
                </div>

                <div class="{{ box.mobileTags and box.mobile_usps|length > 0 ? 'hidden md:block' : box.mobileTags ? 'block' : 'hidden md:block' }}">
                    {% if box.usps|length > 0 %}
                        <div class="md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-4 my-6 md:my-0 divide-y md:divide-y-0 divide-gray-200">
                            {% for item in box.usps %}
                                <div class="py-2 md:py-2.5 md:px-5 md:bg-white flex gap-3 md:gap-4 items-center md:rounded-lg">
                                    <div class="text-apple-700 text-xl">
                                        <i class="ph-fill {{ item.icon }}"></i>
                                    </div>
                                    <div class="tracking-tight text-lg md:text-base font-medium">
                                        {{ item.text }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {% if box.mobileTags and box.mobile_usps|length > 0 %}
                    <div class="block md:hidden">
                        <div class="md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-4 my-6 md:my-0 divide-y md:divide-y-0 divide-gray-200">
                            {% for item in box.mobile_usps %}
                                <div class="py-2 md:py-2.5 md:px-5 md:bg-white flex gap-3 md:gap-4 items-center md:rounded-lg">
                                    <div class="text-apple-700 text-xl">
                                        <i class="ph-fill {{ item.icon }}"></i>
                                    </div>
                                    <div class="tracking-tight text-lg md:text-base font-medium">
                                        {{ item.text }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <div id="hero-2025-formcontainer" class="container {{ box.title_above_form ? 'pt-12 md:pt-16' : 'md:mt-8' }} lg:pt-0">
            <div class="md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-4 lg:gap-8  md:px-4 xl:px-0">
                <div class="md:col-span-2">
                    <div
                        id="form_wrapper"
                        class="{{ box.title_above_form ? 'formHasTitleAbove' : 'overflow-hidden' }} bg-white rounded-lg p-6 {{ box.veiligeVerbinding ? 'pb-20 md:p-12 md:pb-20' : 'pb-12 md:p-12 md:pb-20' }} mb-10 md:mb-6 lg:mb-16 relative z-20 {{ not isFocusLayout ? 'shadow-form md:shadow-xl' : 'transition-all ease-in duration-200 md:shadow-xl' }}"
                        x-trap="formFocus"
                        {% if not input('bedankt') %}@mousedown="formFocus = true" {% endif %}
                        {% if isFocusLayout %}
                            x-intersect.threshold.90="formInViewport = true"
                            x-intersect:leave.threshold.20="formInViewport = false"
                            :class="{ 'shadow-form': !formInViewport, '-mx-8 px-12 md:mx-0': formInViewport }"
                        {% endif %}
                    >
                        {# <div id="form_default" x-trap="formFocus"> #}
                        <div id="form_default"
                            x-intersect:enter="hasSeenForm = true, ctaBanner = false"
                            x-intersect:leave="if (hasSeenForm && scrolled > formEnd) ctaBanner = true; if (formEnd === 0) formEnd = window.pageYOffset || (document.documentElement || document.body.parentNode || document.body).scrollTop">

                            {% if box.title_above_form %}
                                <div
                                    class="bg-gray-100 border border-gray-200 -m-4 mb-6 px-5 md:-m-10 md:mb-8 md:px-10 rounded-lg relative z-50"
                                    {% if isFocusLayout %}
                                        {# :class="{
                                            'block md:block': formInViewport,
                                            'hidden md:block': !formInViewport
                                        }" #}
                                        x-show=
                                        x-cloak
                                    {% endif %}
                                >
                                    <div class="flex justify-center relative">
                                        <div class="w-24 h-24 rounded-full bg-white flex items-center justify-center overflow-hidden ring-[4px] ring-white -translate-y-1/2 absolute left-1/2 -translate-x-1/2 z-50">
                                            <img src="{{ box.form_avatar|media|resize(100, 100, { mode:'crop',extension:'webp',filename:true,quality:100 }) }}" alt="" class="w-full h-full object-cover">
                                        </div>
                                    </div>
                                    <div class="text-xl text-center font-medium py-5 mt-10">
                                        {{ box.title_above_form }}
                                    </div>
                                </div>
                            {% endif %}

                            {% ajaxPartial 'site/dynamic-form' formcode=box.formcode %}

                            {% if box.form_alert_bg %}
                                <style>
                                    #form_default .form_alert .form_alert_bg {
                                        background-color: {{ box.form_alert_bg }};
                                    }
                                    #form_default .form_alert .form_alert_text {
                                        color: {{ box.form_alert_text }};
                                    }
                                </style>
                            {% endif %}

                        </div>
                        <div class="absolute inset-x-0 bottom-0 py-1.5 bg-gray-100">
                            <div class="flex px-5 md:px-12 {{ box.veiligeVerbinding ? 'justify-between' : 'justify-center' }} md:items-center">
                                {% if box.veiligeVerbinding %}
                                    <div class="flex space-x-2 items-center justify-center md:justify-start">
                                        <div class="w-4 h-4 md:w-6 md:h-6 rounded-full bg-gray-300 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-2 h-2 md:w-4 md:h-4 text-white">
                                                <path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-800 text-center text-xs md:text-lg">
                                            {{ box.veiligeVerbindingTekst | default('Veilige verbinding')|replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie}) }}
                                        </span>
                                    </div>
                                {% endif %}

                                {% if box.textUnderForm %}
                                    <p class="text-gray-800 text-center text-xs md:text-lg ">{{ box.textUnderForm|replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie}) }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="hidden md:block fixed inset-0 bg-black/30 z-10" x-show="formFocus" @click="formFocus = false" x-cloak></div>
                </div>
                <div class="md:col-span-2 lg:col-span-1 md:grid md:grid-cols-2 md:gap-8 lg:block" id="hero-2025-sidebar">
                    {% for child in box.children %}
                        <div>
                            {{ child.renderMergeContext(context, { loop: loop }) | replace({'::|plaatsnaam|::': boxesPage.custom_config.plaatsnaam, '::|provincie|::': boxesPage.custom_config.provincie}) | raw }}
                        </div>
                    {% endfor %}
                </div>
            </div>
            {% if not box.hide_reviews %}
                <div id="hero-2025-reviews">
                    {% if box.review_type == "custom" %}
                        {% partial 'klantenvertellenFormFeed/custom-v3' reviews=box.custom_reviews custom_rating_score=box.custom_rating_score custom_rating_text=box.custom_rating_text %}
                    {% else %}
                        {% component 'klantenvertellenFormFeed' %}
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
