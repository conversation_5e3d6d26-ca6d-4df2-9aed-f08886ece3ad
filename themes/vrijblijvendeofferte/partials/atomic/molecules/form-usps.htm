<div class="{{ box.mobileTags and box.mobile_usps|length > 0 ? 'hidden md:block' : box.mobileTags ? 'block' : 'hidden md:block' }}">
    {% if box.usps|length > 0 %}
        <div class="md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-4 my-6 md:my-0 divide-y md:divide-y-0 divide-gray-200">
            {% for item in box.usps %}
                <div class="py-2 md:py-2.5 md:px-5 md:bg-white flex gap-3 md:gap-4 items-center md:rounded-lg">
                    <div class="text-apple-700 text-xl">
                        <i class="ph-fill {{ item.icon }}"></i>
                    </div>
                    <div class="tracking-tight text-lg md:text-base font-medium">
                        {{ item.text }}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
