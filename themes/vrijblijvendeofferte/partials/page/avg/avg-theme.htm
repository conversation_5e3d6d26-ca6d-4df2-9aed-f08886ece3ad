<div id="tos_privacy_theme" class="flex space-x-3 lg:space-x-8">
    {% if this.theme.tos_title %}
        <div x-data="openClose">
            <span class="block text-gray-600 hover:text-gray-700 hover:underline cursor-pointer" @click="toggle">{{ this.theme.tos_link }}</span>
            {% partial 'ui/modal' body %}
                <div class="w-full mb-4 pb-4 border-b">
                <h2 class="mb-0">{{ this.theme.tos_title }}</h2>
                </div>
                <div class="mt-6 prose prose-primary prose-lg text-gray-800 max-w-none">
                {{ this.theme.terms_of_service | content }}
                </div>
                <div class="w-full border-t mt-8 pt-4">
                <button type="button" @click="close" class="py-3 px-6 border shadow-sm bg-white text-gray-600 font-medium rounded-md hover:bg-gray-50 hover:text-gray-800">{{ 'Venster sluiten'|_ }}</button>
                </div>
            {% endpartial %}
        </div>
    {% endif %}

    {% if this.theme.privacy_title %}
        <div x-data="openClose">
            <span class="block text-gray-600 hover:text-gray-700 hover:underline cursor-pointer" @click="toggle">{{ this.theme.privacy_link }}</span>
            {% partial 'ui/modal' body %}
                <div class="w-full mb-4 pb-4 border-b">
                <h2>{{ this.theme.privacy_title }}</h2>
                </div>
                <div class="mt-6 prose prose-primary prose-lg text-gray-800 max-w-none">
                {{ this.theme.privacy_statement | content }}
                </div>
                <div class="w-full border-t mt-8 pt-4">
                <button type="button" @click="close" class="py-3 px-6 border shadow-sm bg-white text-gray-600 font-medium rounded-md hover:bg-gray-50 hover:text-gray-800">{{ 'Venster sluiten'|_ }}</button>
                </div>
            {% endpartial %}
        </div>
    {% endif %}
</div>
