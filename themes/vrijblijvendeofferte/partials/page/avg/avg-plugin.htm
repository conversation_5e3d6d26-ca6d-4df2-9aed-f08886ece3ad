{% set urlRoot = this.request.root() %}
{% set urlClean = urlRoot | replace({'https://': '', 'http://': '', 'www.': ''}) | title %}

<div id="tos_privacy_popup" class="flex flex-col lg:flex-row {{ footer == 'clean' ? 'space-y-1' : 'space-y-4' }} lg:space-y-0 lg:space-x-8">
    {% if tos_privacy_language == 'nl' %}
        {% partial 'page/avg/nl' %}
    {% elseif tos_privacy_language == 'nl-be' %}
        {% partial 'page/avg/be' %}
    {% elseif tos_privacy_language == 'de' %}
        {% partial 'page/avg/de' %}
    {% elseif tos_privacy_language == 'en' %}
        {% partial 'page/avg/en' %}
    {% elseif tos_privacy_language == 'fr' %}
        {% partial 'page/avg/fr' %}
    {% elseif tos_privacy_language == 'be-fr' %}
        {% partial 'page/avg/be-fr' %}
    {% elseif tos_privacy_language == 'esp' %}
        {% partial 'page/avg/esp' %}
    {% elseif tos_privacy_language == 'us' %}
        {% partial 'page/avg/us' %}
    {% else %}
        {% partial 'page/avg/default' %}
    {% endif %}
</div>
