page_title:
    name: <PERSON><PERSON><PERSON> titel
    description: Basis h1 titel voor de paginakop
    nameFrom: name
    icon: icon-header
    fields:
        title:
            label: Titel
            type: richeditor
            toolbarButtons: bold|italic|inlineStyle
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs

breadcrumbs:
    name: Breadcrumbs
    description: Toont een kruimelpad vanaf home naar huidige pagina
    icon: icon-reply-all
    fields:
        background_color:
            label: Achtergrond kleur
            type: colorpicker
            availableColors: ['#ffffff','#fafaf9','#f5f5f5','#e5e5e5','#d4d4d4','#737373','#525252']
            span: row
            spanClass: col-md-4
            default: '#ffffff'
        link_color:
            label: <PERSON><PERSON><PERSON> van kru<PERSON>lpad links
            type: colorpicker
            availableColors: ['#16588d','#00abf0','#fafaf9','#f5f5f5','#e5e5e5','#d4d4d4','#737373','#525252']
            span: row
            spanClass: col-md-4
            default: '#737373'
        active_color:
            label: K<PERSON><PERSON> van actieve pagina
            type: colorpicker
            availableColors: ['#16588d','#00abf0','#fafaf9','#f5f5f5','#e5e5e5','#d4d4d4','#737373','#525252']
            span: row
            spanClass: col-md-4
            default: '#525252'
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_single:
    name: Enkel tekst blok
    description: Tekstblok over gehele breedte
    icon: icon-align-left
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        content:
            label: Tekst
            type: richeditor
            size: huge
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_columns:
    name: Tekst blokken
    description: 1 of 2 koloms tekst blok
    icon: icon-align-left
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        content:
            label: Tekst kolom
            type: repeater
            maxItems: 2
            prompt: Nieuw tekst blok
            form:
                fields:
                    text:
                        label: Tekst
                        type: richeditor
                        size: huge
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

snippets:
    name: Snippets
    description: Voeg een formulier of module aan de pagina toe
    nameFrom: title
    icon: icon-newspaper-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        container:
            label: Container
            type: checkbox
        snippet:
            label: Snippet
            type: richeditor
            toolbarButtons: bold|italic|insertSnippet
            size: huge
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_slider:
    name: Tekst met slider
    description: Foto slider met tekst rechts
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        _ruler_start:
            type: ruler
        _section_content:
            label: Content
            type: section
        content:
            label: Content
            type: richeditor
            size: huge
        _ruler_content:
            type: ruler
        _section_lijst:
            label: Lijst
            type: section
        has_list:
            label: Toon voordelen lijst
            type: checkbox
            default: false
        list_title:
            label: Titel boven lijst
            trigger:
                action: show
                field: has_list
                condition: checked
        list:
            label: Lijst
            type: repeater
            span: left
            prompt: Nieuw lijst item toevoegen
            style: accordion
            titleFrom: list_item
            trigger:
                action: show
                field: has_list
                condition: checked
            form:
                fields:
                    list_item:
                        label: Lijst item
        _ruler_list:
            type: ruler
        _section_button:
            label: Knoppen
            type: section
        has_buttons:
            label: Toon knoppen
            type: checkbox
            default: false
        buttons:
            label: Knoppen
            type: repeater
            style: accordion
            titleFrom: text
            span: left
            trigger:
                action: show
                field: has_buttons
                condition: checked
            form:
                fields:
                    text:
                        label: Tekst
                        span: auto
                    url:
                        label: Link
                        span: auto
                    style:
                        label: Stijl
                        type: dropdown
                        default: primary
                        span: auto
                        options:
                            primary: Primair
                            secondary: Secundair
                            tertiary: Tertiar
                    size:
                        label: Grootte
                        type: dropdown
                        default: base
                        span: auto
                        options:
                            sm: Klein
                            base: Normaal
                            lg: Groot
                    icon:
                        label: Icoon
                    external_link:
                        label: Externe link
                        type: checkbox
                        comment: Open link in nieuw tabblad
                    offerte_button:
                        label: Focus op eerste veld
                        description: Vink aan als knop het eerste veld van het formulier actief moet maken
                        type: checkbox
                        default: false
                    link_button:
                        label: Link naar een andere pagina
                        type: checkbox
                        description: Vink dit aan als de knop naar een andere pagina verwijst
        _ruler_buttons:
            type: ruler
        _section_slider:
            label: Slider
            type: section
        slider_button_text:
            label: Slider knop tekst
            span: auto
        slider_button_url:
            label: Slider knop url
            span: auto
        slider_images_crop:
            label: Positionering afbeeldingen
            type: dropdown
            options:
                container: In vast frame (grijze achtergrond)
                full_size: Slide helemaal opgevuld (geen achtergrond)
        position:
            label: Slider positie
            type: dropdown
            options:
                left: Links
                right: Rechts
            default: left
        slider_width:
            label: Slider breedte
            type: dropdown
            options:
                1_column: 1 kolom (25%)
                2_columns: 2 kolommen (50%)
                3_columns: 3 kolommen (75%)
                4_columns: 4 kolommen (100%)
            default: 2_columns
            comment: 2 kolommen = 600x600(px) afbeelding, 3 kolommen = 850x500(px) afbeelding
        _ruler_slides0:
            type: ruler
        slides:
            label: Slides
            type: repeater
            style: accordion
            titleFrom: description
            span: left
            form:
                fields:
                    image:
                        label: Afbeelding
                        type: mediafinder
                        mode: image
                    description:
                        label: Alt tag
        _ruler_slides1:
            type: ruler
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_logo:
    name: Tekst met logo
    description: Tekst links met logo rechts
    icon: icon-file-text-o
    fields:
        title:
            label: Titel
        content:
            label: Content
            type: richeditor
            size: huge
        logo:
            label: Logo
            type: mediafinder
            mode: image
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_image:
    name: Tekst met afbeelding
    description: Tekst met een foto rechts
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        _section_content:
            label: Content
            type: section
        content:
            label: Content
            type: richeditor
            size: huge
        _section_lijst:
            label: Lijst
            type: section
        has_list:
            label: Toon voordelen lijst
            type: checkbox
            default: false
        list_title:
            label: Titel boven lijst
            trigger:
                action: show
                field: has_list
                condition: checked
        list:
            label: Lijst
            type: repeater
            prompt: Nieuw lijst item toevoegen
            trigger:
                action: show
                field: has_list
                condition: checked
            form:
                fields:
                    list_item:
                        label: Lijst item
        _section_button:
            label: Knoppen
            type: section
        has_buttons:
            label: Toon knoppen
            type: checkbox
            default: false
        buttons:
            label: Knoppen
            type: repeater
            trigger:
                action: show
                field: has_buttons
                condition: checked
            form:
                fields:
                    text:
                        label: Tekst
                        span: auto
                    url:
                        label: Link
                        span: auto
                        trigger:
                            action: show
                            field: button_type
                            condition: value[link]
                    style:
                        label: Stijl
                        type: dropdown
                        default: primary
                        span: auto
                        options:
                            primary: Primair
                            secondary: Secundair
                            tertiary: Tertiar
                    size:
                        label: Grootte
                        type: dropdown
                        default: base
                        span: auto
                        options:
                            sm: Klein
                            base: Normaal
                            lg: Groot
                    icon:
                        label: Icoon
                    button_type:
                        label: Knop type
                        type: balloon-selector
                        options:
                            button: Focus op eerste veld
                            link: Link naar een andere pagina
                        default: button
                    external_link:
                        label: Externe link
                        type: checkbox
                        comment: Open link in nieuw tabblad
                        trigger:
                            action: show
                            field: button_type
                            condition: value[link]
                    offerte_button:
                        label: Focus op eerste veld
                        description: Vink aan als knop het eerste veld van het formulier actief moet maken
                        type: checkbox
                        default: false
                        hidden: true
                    link_button:
                        label: Link naar een andere pagina
                        type: checkbox
                        description: Vink dit aan als de knop naar een andere pagina verwijst
                        hidden: true
        _section_image:
            label: Afbeelding
            type: section
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        shadow:
            label: Schaduw
            type: switch
            on: Aan
            off: Uit
        ratio:
            label: Ratio behouden
            type: switch
            on: Aan
            off: Uit
            default: off
        position:
            label: Afbeelding positie
            type: dropdown
            default: right
            options:
                right: Rechts
                left: Links
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

text_image_simple:
    name: Tekst met afbeelding simpel
    description: Tekst met een foto rechts
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        _section_content:
            label: Content
            type: section
        content:
            label: Content
            type: richeditor
            size: huge
        _section_button:
            label: Knoppen
            type: section
        has_buttons:
            label: Toon knoppen
            type: checkbox
            default: false
        buttons:
            label: Knoppen
            type: repeater
            trigger:
                action: show
                field: has_buttons
                condition: checked
            form:
                fields:
                    text:
                        label: Tekst
                        span: auto
                    url:
                        label: Link
                        span: auto
                    style:
                        label: Stijl
                        type: dropdown
                        default: primary
                        span: auto
                        options:
                            primary: Primair
                            secondary: Secundair
                            tertiary: Tertiar
                    size:
                        label: Grootte
                        type: dropdown
                        default: base
                        span: auto
                        options:
                            sm: Klein
                            base: Normaal
                            lg: Groot
                    icon:
                        label: Icoon
                    external_link:
                        label: Externe link
                        type: checkbox
                        comment: Open link in nieuw tabblad
                    offerte_button:
                        label: Focus op eerste veld
                        description: Vink aan als knop het eerste veld van het formulier actief moet maken
                        type: checkbox
                        default: false
                    link_button:
                        label: Link naar een andere pagina
                        type: checkbox
                        description: Vink dit aan als de knop naar een andere pagina verwijst
        _section_image:
            label: Afbeelding
            type: section
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        ratio:
            label: Ratio behouden
            type: switch
            on: Aan
            off: Uit
            default: off
        position:
            label: Afbeelding positie
            type: dropdown
            default: right
            options:
                right: Rechts
                left: Links
        sticker_text:
            label: Sticker tekst
            type: richeditor
            size: huge
            toolbarButtons: inlineClass|html|clearFormatting|undo|redo
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

cards_steps:
    name: Kaarten - Stappen
    description: Stappenplan in kaarten
    icon: icon-list-alt
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        steps:
            label: Stappen
            type: repeater
            prompt: Nieuwe stap toevoegen
            form:
                fields:
                    title:
                        label: Titel
                        span: auto
                    icon:
                        label: Icoon
                        span: auto
                    content:
                        label: Tekst
                        type: textarea
                        span: full
        button_text:
            label: Knop tekst
            span: auto
        button_url:
            label: Knop link
            span: auto
        offerte_button:
            label: Focus op eerste veld
            description: DEZE NIET MEER GEBRUIKEN
            type: checkbox
            default: false
        link_button:
            label: Link naar een andere pagina
            type: checkbox
            description: Vink dit aan als de knop naar een andere pagina verwijst
        external_link:
            label: Externe link
            type: checkbox
            comment: Open link in nieuw tabblad
        show_line:
            label: Toon lijn achter titel en knop
            type: switch
            on: Tonen
            off: Niet tonen
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

categories:
    name: Categorieen
    description: Toont alle Categorieen
    icon: icon-th
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

latest_blog_posts:
    name: Laatste blogberichten
    description: Toont de laatste 3 blogberichten
    icon: icon-comment
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        button_text:
            label: Knop tekst
            span: auto
        button_url:
            label: Knop link
            span: auto
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml
incentive_lg:
    name: Aansporing groot
    description: Grote aansporing met titel tekst en iconen
    icon: icon-check-square-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        content:
            label: Content
            type: richeditor
            size: huge
        incentive_cards:
            label: Aansporingen
            type: repeater
            prompt: Nieuw aansporing toevoegen
            form:
                fields:
                    icon:
                        label: Icoon
                    title:
                        label: Titel
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

incentive_two_columns:
    name: Aansporing 2 kolommen
    description: Aansporing in 2 kolommen, afb. links/tekst rechts
    icon: icon-check-square-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        incentive_title:
            label: Aansporing titel
            placeholder: Uw voordelen
        incentive_list:
            label: Aansporing lijst
            type: repeater
            prompt: Nieuw lijst item toevoegen
            form:
                fields:
                    list_item:
                        label: Lijst item
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

usp_list:
    name: USP lijst
    description: Banner met 4 unique selling points
    icon: icon-star
    fields:
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        usp:
            label: Unique selling points
            type: repeater
            maxItems: 4
            form:
                fields:
                    icon:
                        label: Icoon
                    text:
                        label: Tekst
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

klantenvertellen_slider:
    name: Klanten vertellen slider
    description: toont de laatste reviews in een slider
    icon: icon-comment-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

partner_last_entries:
    name: Partner logo's + Laaste aanvragen
    description: Partner logo's + Laaste aanvragen
    icon: icon-cloud
    fields:
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        _section_logos:
            label: Logo's
            type: section
        title_logos:
            label: Titel boven logo's
        last_column_text:
            label: Laatste kolom tekst
            type: richeditor
            size: huge
        logos:
            label: Logo's
            type: repeater
            form:
                fields:
                    image:
                        label: Afbeelding
                        type: mediafinder
                        mode: image
                    url:
                        label: Link
                        type: text
        _section_entries:
            label: Aanvragen
            type: section
        title_entries:
            label: Titel boven aanvragen
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

cta_banner:
    name: Call to Action banner
    description: Banner met illustratie
    icon: icon-star
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        text:
            label: Tekst
            type: textarea
        button_text:
            label: Button tekst
            span: left
        button_url:
            label: Button url
            span: right
        image:
            label: Illustratie
            type: mediafinder
            mode: image
        external_link:
            label: Externe link
            type: checkbox
            comment: Open link in nieuw tabblad
        offerte_button:
            label: Focus op eerste veld
            comment: Vink aan als knop het eerste veld van het formulier actief moet maken
            type: checkbox
            default: false
        link_button:
            label: Link naar een andere pagina
            type: checkbox
            description: Vink dit aan als de knop naar een andere pagina verwijst
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

cta_banner_cartoon:
    name: Cartoon banner
    description: Call to action banner met cartoon
    icon: icon-star
    fields:
        title:
            label: Titel
        text:
            label: Tekst
        button_text:
            label: Button tekst
            span: left
        button_url:
            label: Button url
            span: right
        external_link:
            label: Externe link
            type: checkbox
            comment: Open link in nieuw tabblad
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        image_offset_y:
            label: Positie vanaf onderkant
            type: number
            default: 18
            span: left
        image_offset_x:
            label: Positie vanaf rechterkant
            type: number
            default: 24
            span: right
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

partner_contact:
    name: Partner contact formulier
    description: contactformulier voor partner pagina
    icon: icon-wpforms
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: gray
            options:
                white: Wit
                gray: Licht grijs
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

page_heading:
    name: Pagina header
    description: Pagina header met achtergrond
    icon: icon-header
    fields:
        title:
            label: Titel
            type: richeditor
            size: huge
            toolbarButtons: bold|italic|inlineStyle
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        # overlay_color:
        #     label: Kleur overlay
        #     type: colorpicker
        #     availableColors: ['#16588d','#00abf0']

page_heading_icon:
    name: Pagina header (hypotheekrente)
    description: Pagina header met huis/boom/vogels icoon
    icon: icon-header
    fields:
        title:
            label: Titel
            type: richeditor
            size: huge
            toolbarButtons: bold|italic|inlineStyle
        text:
            label: Tekst
            type: richeditor
            size: huge
        # overlay_color:
        #     label: Kleur overlay
        #     type: colorpicker
        #     availableColors: ['#16588d','#00abf0']

content_lg:
    name: Content groot
    description: Meerdere alinea's en afbeeldingne
    icon: icon-align-left
    fields:
        title:
            label: Titel
        intro:
            label: Intro
            type: richeditor
            size: huge
        content:
            type: repeater
            span: left
            form:
                fields:
                    title:
                        label: Titel
                    icoon:
                        label: Icoon
                    text:
                        label: Intro
                        type: richeditor
                        size: huge
        images:
            label: Afbeeldingen
            type: mediafinder
            mode: image
            maxItems: 3
            span: right
        button_text:
            label: Knop tekst
            span: left
        button_url:
            label: Knop url
            span: right
        external_link:
            label: Externe link
            type: checkbox
            comment: Open link in nieuw tabblad
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

slider_list:
    name: Slider met knoppen lijst
    description: Foto slider met knoppen lijst
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        _section_content:
            label: Content
            type: section
        content:
            label: Content
            type: richeditor
            size: huge
        _section_lijst:
            label: Lijst
            type: section
        has_list:
            label: Toon voordelen lijst
            type: checkbox
            default: false
        list_title:
            label: Titel boven lijst
            trigger:
                action: show
                field: has_list
                condition: checked
        list:
            label: Lijst
            type: repeater
            prompt: Nieuw lijst item toevoegen
            trigger:
                action: show
                field: has_list
                condition: checked
            form:
                fields:
                    list_item:
                        label: Lijst item
        _section_slider:
            label: Slider
            type: section
        slides:
            label: Slides
            type: repeater
            form:
                fields:
                    title:
                        label: Titel
                        span: auto
                    image:
                        label: Afbeelding
                        type: mediafinder
                        mode: image
                        span: auto
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

cta_banner_overlay:
    name: Afbeelding banner overlap
    description: Call to action banner met overlap tekst
    icon: icon-star
    fields:
        title:
            label: Titel
        text:
            label: Tekst
            type: richeditor
            size: huge
        button_text:
            label: Button tekst
            span: left
        button_url:
            label: Button url
            span: right
        external_link:
            label: Externe link
            type: checkbox
            comment: Open link in nieuw tabblad
        image:
            label: Afbeelding
            type: mediafinder
            mode: image
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

hypotheek_rentes:
    name: Hypotheek rente overzicht
    description: Overzicht met alle hypotheekrentes
    icon: icon-table

hypotheek_rentes_bank:
    name: Hypotheekrente's per bank
    description: Overzicht met hypotheekrentes per bank
    icon: icon-table

stats_banner:
    name: Statistieken banner
    description: Banner met statistieken
    icon: icon-line-chart
    fields:
        stats:
            type: repeater
            prompt: Nieuwe statistiek
            maxItems: 4
            form:
                fields:
                    icon:
                        label: Icoon
                        comment: FontAwesome icoon code (user-group)
                    value:
                        label: Waarde
                    text:
                        label: Tekst
                        type: textarea

text_dynamic:
    name: Tekst met dynamische inhoud
    description: Foto slider met tekst rechts
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        _section_content:
            label: Content
            type: section
        content:
            label: Content
            type: richeditor
            size: huge
        _section_lijst:
            label: Lijst
            type: section
        has_list:
            label: Toon voordelen lijst
            type: checkbox
            default: false
        list_title:
            label: Titel boven lijst
            trigger:
                action: show
                field: has_list
                condition: checked
        list:
            label: Lijst
            type: repeater
            prompt: Nieuw lijst item toevoegen
            trigger:
                action: show
                field: has_list
                condition: checked
            form:
                fields:
                    list_item:
                        label: Lijst item
        _section_button:
            label: Knoppen
            type: section
        has_buttons:
            label: Toon knoppen
            type: checkbox
            default: false
        buttons:
            label: Knoppen
            type: repeater
            trigger:
                action: show
                field: has_buttons
                condition: checked
            form:
                fields:
                    text:
                        label: Tekst
                        span: auto
                    url:
                        label: Link
                        span: auto
                    style:
                        label: Stijl
                        type: dropdown
                        default: primary
                        span: auto
                        options:
                            primary: Primair
                            secondary: Secundair
                            tertiary: Tertiar
                    size:
                        label: Grootte
                        type: dropdown
                        default: base
                        span: auto
                        options:
                            sm: Klein
                            base: Normaal
                            lg: Groot
                    icon:
                        label: Icoon
                    external_link:
                        label: Externe link
                        type: checkbox
                        comment: Open link in nieuw tabblad
                    offerte_button:
                        label: Focus op eerste veld
                        description: Vink aan als knop het eerste veld van het formulier actief moet maken
                        type: checkbox
                        default: false
                    link_button:
                        label: Link naar een andere pagina
                        type: checkbox
                        description: Vink dit aan als de knop naar een andere pagina verwijst
        _section_dynamic:
            label: Dynamische inhoud
            type: section
        dynamic_content:
            label: Dynamische inhoud
            type: richeditor
            size: huge
        position:
            label: Dynamische inhoud positie
            type: dropdown
            default: right
            options:
                right: Rechts
                left: Links
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

media_slider:
    name: Slider van mediaberichten
    description: Slider van mediaberichten
    icon: icon-picture-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        _section_slider:
            label: Slider
            type: section
        slides:
            type: repeater
            prompts: Nieuwe slide
            form:
                fields:
                    title:
                        label: Titel
                    image:
                        label: Afbeelding
                        type: mediafinder
                        mode: image
                    source_name:
                        label: Bron naam
                        span: auto
                    source_url:
                        label: Bron url
                        span: auto
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

snippet_faq:
    name: Veelgestelde vragen
    icon: icon-question-circle-o
    fields:
        title:
            label: Titel
        background:
            label: Achtergrond kleur
            type: dropdown
            default: white
            options:
                white: Wit
                gray: Licht grijs
        columns:
            label: Kolommen
            type: dropdown
            options:
                single: 1 kolom
                double: 2 kolommen
        questions:
            type: repeater
            label: Vragen
            prompt: Vraag toevoegen
            nameFrom: question
            displayMode: builder
            span: left
            form:
                fields:
                    question:
                        label: Vraag
                    answer:
                        label: Antwoord
                        type: richeditor
        questions_right:
            type: repeater
            label: Vragen
            prompt: Vraag toevoegen
            nameFrom: question
            displayMode: builder
            span: right
            form:
                fields:
                    question:
                        label: Vraag
                    answer:
                        label: Antwoord
                        type: richeditor
        responsive:
            type: nestedform
            showPanel: false
            form: $/../themes/vrijblijvendeofferte/repeaters/includes/responsive.yaml

product_list:
    name: Producten overzicht
    description: Overzicht van producten
    icon: icon-cart-plus
